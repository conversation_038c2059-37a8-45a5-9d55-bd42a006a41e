services:
  mysql:
    image: mysql:8.0
    container_name: bugconform_mysql
    restart: unless-stopped
    env_file:
      - ./backend/.env
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-root123}
      MYSQL_DATABASE: ${DB_NAME:-bugconform}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - ./backend/mysql_data:/var/lib/mysql
      - ./backend/mysql_init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - bugconform_network

networks:
  bugconform_network:
    driver: bridge
