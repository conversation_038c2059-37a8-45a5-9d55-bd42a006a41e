#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标题评估功能（包含正则提取）的脚本
"""
import os
import sys
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from backend.app.service.title_score import TitleFieldEvaluation, evaluate_title_field

async def test_title_evaluation_with_regex():
    """测试标题评估功能，验证正则提取是否正常工作"""
    print("=== 测试标题评估功能（包含正则提取） ===")
    
    # 测试数据 - 选择一个可能导致JSON解析问题的标题
    test_title = "【腾讯健康】全局搜索敏感问题，搜索结果页健康问问兜底样式不正确"
    
    print(f"测试标题: {test_title}")
    print("开始评估...")
    
    try:
        # 调用标题评估函数
        result = await evaluate_title_field("标题", test_title)
        
        print("\n=== 评估结果 ===")
        print(f"字段: {result.field}")
        print(f"维度评分: {result.dimension_scores}")
        print(f"是否通过: {result.passed}")
        print(f"反馈: {result.feedback}")
        print(f"建议: {result.suggest}")
        print(f"阶段1思维过程长度: {len(result.thinking_stage1)} 字符")
        print(f"阶段2思维过程长度: {len(result.thinking_stage2)} 字符")

        # 显示思维过程的前100个字符（如果存在）
        if result.thinking_stage1:
            preview1 = result.thinking_stage1[:100] + "..." if len(result.thinking_stage1) > 100 else result.thinking_stage1
            print(f"阶段1思维过程预览: {preview1}")

        if result.thinking_stage2:
            preview2 = result.thinking_stage2[:100] + "..." if len(result.thinking_stage2) > 100 else result.thinking_stage2
            print(f"阶段2思维过程预览: {preview2}")

        # 验证新增字段是否存在且有内容
        assert hasattr(result, 'thinking_stage1'), "缺少阶段1思维过程字段"
        assert hasattr(result, 'thinking_stage2'), "缺少阶段2思维过程字段"

        # 检查是否成功提取到思维过程（至少阶段1应该有内容）
        if result.thinking_stage1:
            print("\n✅ 测试通过！成功提取到阶段1思维过程")
        else:
            print("\n⚠️  警告：阶段1思维过程为空，可能需要检查正则提取逻辑")

        if not result.passed and result.thinking_stage2:
            print("✅ 阶段2思维过程也成功提取")
        elif not result.passed and not result.thinking_stage2:
            print("⚠️  警告：标题未通过但阶段2思维过程为空")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_title_evaluation_with_regex())
