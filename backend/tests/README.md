# 测试用例目录

本目录包含了BugConform项目的各种测试用例，用于验证系统功能的正确性。

## 测试文件说明

### 1. test_thinking_process_extraction.py
**功能**: 测试思维过程字段的正则表达式提取功能
**用途**: 验证在LLM返回格式不标准时，系统能否正确提取thinking_process字段
**测试场景**:
- 标准JSON格式的thinking_process提取
- 包含转义字符的JSON格式处理
- 多行思维过程内容的提取
- 格式不规范JSON的容错处理

**运行方式**:
```bash
cd /data/home/<USER>/BugConform
python3 tests/test_thinking_process_extraction.py
```

### 2. test_title_evaluation_with_regex.py
**功能**: 测试标题评估功能（包含正则提取）
**用途**: 验证标题评估的完整流程，特别是思维过程数据的提取
**测试场景**:
- 标题质量评估的两阶段流程
- 思维过程数据的正确提取和存储
- 异常情况下的容错处理

**运行方式**:
```bash
cd /data/home/<USER>/BugConform
python3 tests/test_title_evaluation_with_regex.py
```

## 测试环境要求

- Python 3.11+
- 项目依赖包已安装（见backend/requirements.txt）
- 配置文件正确设置（API密钥等）

## 添加新测试用例

1. 在tests目录下创建新的测试文件
2. 文件命名规范：`test_<功能名称>.py`
3. 在文件开头添加适当的注释说明测试目的
4. 更新本README文件，添加新测试用例的说明

## 注意事项

- 测试文件中的路径引用需要相对于项目根目录
- 确保测试不会影响生产数据
- 测试完成后及时清理临时文件
