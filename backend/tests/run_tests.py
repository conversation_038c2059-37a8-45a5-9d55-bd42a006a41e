#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 批量运行所有测试用例
"""
import os
import sys
import subprocess
import glob
from pathlib import Path

def run_test_file(test_file):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        # 切换到项目根目录运行测试
        project_root = Path(__file__).parent.parent
        result = subprocess.run(
            [sys.executable, test_file],
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        if result.returncode == 0:
            print("✅ 测试通过")
            print(result.stdout)
        else:
            print("❌ 测试失败")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {str(e)}")
        return False

def main():
    """主函数 - 运行所有测试"""
    print("🧪 BugConform 测试套件")
    print("开始运行所有测试用例...")
    
    # 获取tests目录下所有test_*.py文件
    tests_dir = Path(__file__).parent
    test_files = list(tests_dir.glob("test_*.py"))
    
    if not test_files:
        print("❌ 没有找到测试文件")
        return
    
    print(f"发现 {len(test_files)} 个测试文件:")
    for test_file in test_files:
        print(f"  - {test_file.name}")
    
    # 运行所有测试
    passed = 0
    failed = 0
    
    for test_file in test_files:
        if run_test_file(test_file):
            passed += 1
        else:
            failed += 1
    
    # 输出总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试都通过了！")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查上述输出")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
