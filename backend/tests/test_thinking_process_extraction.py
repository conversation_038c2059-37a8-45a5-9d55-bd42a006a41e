#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试思维过程正则提取功能的脚本
"""
import os
import sys
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

def test_thinking_process_extraction():
    """测试思维过程字段的正则提取功能"""
    print("=== 测试思维过程正则提取功能 ===")
    
    # 模拟各种格式的LLM返回内容
    test_cases = [
        {
            "name": "标准JSON格式",
            "content": '''
            {
                "thinking_process": "这是一个标准的思维过程内容，包含详细的分析步骤。",
                "dimension_scores": {"准确性": 0.8, "清晰性": 0.9},
                "feedback": "这是反馈内容"
            }
            ''',
            "expected": "这是一个标准的思维过程内容，包含详细的分析步骤。"
        },
        {
            "name": "包含转义字符的JSON",
            "content": '''
            {
                "thinking_process": "这是包含\\n换行符和\\"引号的思维过程内容。",
                "dimension_scores": {"准确性": 0.7}
            }
            ''',
            "expected": "这是包含\n换行符和\"引号的思维过程内容。"
        },
        {
            "name": "多行思维过程",
            "content": '''
            {
                "thinking_process": "第一步：分析标题结构\\n第二步：评估准确性\\n第三步：给出建议",
                "suggest": "建议标题"
            }
            ''',
            "expected": "第一步：分析标题结构\n第二步：评估准确性\n第三步：给出建议"
        },
        {
            "name": "格式不规范的JSON",
            "content": '''
            "thinking_process": "这是格式不规范但包含思维过程的内容",
            "dimension_scores": {"准确性": 0.6}
            ''',
            "expected": "这是格式不规范但包含思维过程的内容"
        }
    ]
    
    # 测试正则提取逻辑
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        content = test_case['content']
        expected = test_case['expected']
        
        # 模拟异常处理中的正则提取逻辑
        thinking_process = ""
        thinking_process_match = re.search(r'"thinking_process"\s*:\s*"((?:\\.|[^"\\])*)"', content)
        if thinking_process_match:
            # 处理转义字符
            thinking_process = thinking_process_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            print(f"✅ 提取成功（JSON格式）: {thinking_process}")
        else:
            # 如果没有找到JSON格式的thinking_process，尝试提取文本形式的思维过程
            thinking_process_patterns = [
                r'"thinking_process"\s*:\s*"([\s\S]+?)"',
                r'"thinking_process"\s*:\s*"([^"]*)"'
            ]
            for pattern in thinking_process_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    thinking_process = match.group(1).strip()
                    print(f"✅ 提取成功（文本格式）: {thinking_process}")
                    break
        
        if not thinking_process:
            print("❌ 提取失败")
        else:
            # 验证提取结果
            if thinking_process == expected:
                print(f"✅ 验证通过: 提取结果与期望一致")
            else:
                print(f"⚠️  验证警告: 提取结果与期望不完全一致")
                print(f"   期望: {expected}")
                print(f"   实际: {thinking_process}")

if __name__ == "__main__":
    test_thinking_process_extraction()
