from dotenv import load_dotenv
import os
import uvicorn
from fastapi import FastAPI
from app.routers import api
from app.database.database import get_engine, Base
from app.database.database import init_db

# ✅ 把 app 放到模块作用域
app = FastAPI(title="FastAPI Backend")
app.include_router(api.router, prefix="/api")

# ✅ 加载环境变量和数据库初始化只在主模块运行时执行
if __name__ == "__main__":

    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=False)
