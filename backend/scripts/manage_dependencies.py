#!/usr/bin/env python3
"""
依赖管理脚本
用于安装、更新和检查项目依赖
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def install_production_deps():
    """安装生产环境依赖"""
    return run_command("pip install -r requirements.txt", "安装生产环境依赖")

def install_dev_deps():
    """安装开发环境依赖"""
    return run_command("pip install -r requirements-dev.txt", "安装开发环境依赖")

def upgrade_deps():
    """升级所有依赖到最新版本"""
    return run_command("pip install --upgrade -r requirements.txt", "升级生产环境依赖")

def check_outdated():
    """检查过时的依赖"""
    return run_command("pip list --outdated", "检查过时的依赖")

def generate_freeze():
    """生成当前环境的依赖快照"""
    return run_command("pip freeze > requirements-freeze.txt", "生成依赖快照")

def check_security():
    """检查安全漏洞"""
    # 首先安装 safety 如果没有的话
    try:
        import safety
    except ImportError:
        print("🔄 安装 safety 工具...")
        subprocess.run("pip install safety", shell=True, check=True)
    
    return run_command("safety check", "检查安全漏洞")

def main():
    """主函数"""
    print("🐍 BugConform 依赖管理工具")
    print("=" * 50)
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    backend_dir = script_dir.parent
    os.chdir(backend_dir)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python scripts/manage_dependencies.py install     # 安装生产环境依赖")
        print("  python scripts/manage_dependencies.py install-dev # 安装开发环境依赖")
        print("  python scripts/manage_dependencies.py upgrade     # 升级依赖")
        print("  python scripts/manage_dependencies.py check       # 检查过时依赖")
        print("  python scripts/manage_dependencies.py freeze      # 生成依赖快照")
        print("  python scripts/manage_dependencies.py security    # 安全检查")
        return
    
    command = sys.argv[1]
    
    if command == "install":
        install_production_deps()
    elif command == "install-dev":
        install_dev_deps()
    elif command == "upgrade":
        upgrade_deps()
    elif command == "check":
        check_outdated()
    elif command == "freeze":
        generate_freeze()
    elif command == "security":
        check_security()
    else:
        print(f"❌ 未知命令: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
