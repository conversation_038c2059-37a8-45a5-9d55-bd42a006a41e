#!/usr/bin/env python3
"""
检查项目依赖是否正确安装
"""

import importlib
import sys
from pathlib import Path

# 定义核心依赖列表
CORE_DEPENDENCIES = [
    ('fastapi', 'FastAPI Web框架'),
    ('uvicorn', 'ASGI服务器'),
    ('pydantic', '数据验证'),
    ('sqlalchemy', 'ORM数据库'),
    ('pymysql', 'MySQL驱动'),
    ('httpx', 'HTTP客户端'),
    ('requests', 'HTTP请求库'),
    ('bs4', 'BeautifulSoup网页解析'),
    ('html2text', 'HTML转文本'),
    ('markdownify', 'HTML转Markdown'),
    ('langchain', 'AI语言链'),
    ('openai', 'OpenAI接口'),
    ('pandas', '数据处理'),
    ('openpyxl', 'Excel处理'),
    ('dotenv', '环境变量'),
    ('pytz', '时区处理'),
    ('Crypto', '加密库'),
]

def check_import(module_name, description):
    """检查单个模块是否可以导入"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description} ({module_name})")
        return True
    except ImportError as e:
        print(f"❌ {description} ({module_name}) - 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 BugConform 依赖检查")
    print("=" * 50)
    
    success_count = 0
    total_count = len(CORE_DEPENDENCIES)
    
    for module_name, description in CORE_DEPENDENCIES:
        if check_import(module_name, description):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 检查结果: {success_count}/{total_count} 依赖可用")
    
    if success_count == total_count:
        print("🎉 所有核心依赖都已正确安装！")
        return 0
    else:
        missing_count = total_count - success_count
        print(f"⚠️  有 {missing_count} 个依赖缺失或无法导入")
        print("\n建议执行以下命令安装缺失的依赖:")
        print("python3 scripts/manage_dependencies.py install")
        return 1

if __name__ == "__main__":
    sys.exit(main())
