#!/usr/bin/env python3
"""
独立的BUG评估数据删除脚本
不依赖项目的模块结构，避免循环导入问题

使用方法:
python standalone_delete_script.py [--preview] [--force]

参数:
--preview: 只预览，不执行删除
--force: 强制删除，不需要确认
"""

import json
import os
import sys
import argparse
from typing import Set, List
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, JSON, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))

# 创建基类
Base = declarative_base()

# 定义数据库模型
class BugEvaluation(Base):
    __tablename__ = "bug_evaluations"
    id = Column(Integer, primary_key=True, index=True)
    bug_id = Column(String(50), index=True)
    workspace_id = Column(String(50), index=True)
    title = Column(String(500))
    creator = Column(String(100))
    status = Column(String(50))
    priority = Column(String(50))
    severity = Column(String(50))
    description = Column(Text)
    module = Column(String(200))
    bug_data = Column(JSON)
    overall_passed = Column(Boolean, default=False)
    common_missing_fields = Column(JSON)
    evaluated_at = Column(DateTime)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class TitleEvaluation(Base):
    __tablename__ = "title_evaluations"
    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)
    field = Column(String(50))
    dimension_scores = Column(JSON)
    passed = Column(Boolean)
    feedback = Column(Text)
    suggest = Column(Text)
    created_at = Column(DateTime)

class DescriptionEvaluation(Base):
    __tablename__ = "description_evaluations"
    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)
    field = Column(String(50))
    dimension_scores = Column(JSON)
    score = Column(Float, default=0.0)
    passed = Column(Boolean)
    feedback = Column(Text)
    suggest = Column(Text)
    created_at = Column(DateTime)

class SuggestEvaluation(Base):
    __tablename__ = "suggest_evaluations"
    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)
    field = Column(String(50))
    suggested = Column(String(100))
    actual = Column(String(100))
    reason = Column(Text)
    feedback = Column(Text)
    created_at = Column(DateTime)


def create_db_connection():
    """创建数据库连接"""
    # 从环境变量获取数据库配置
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_HOST = os.getenv('DB_HOST')
    DB_PORT = os.getenv('DB_PORT')
    DB_NAME = os.getenv('DB_NAME')

    if not all([DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]):
        raise ValueError("环境变量未完全设置，无法初始化数据库连接。")

    # 构建数据库URL
    SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

    # 创建数据库引擎和会话工厂
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=3600,
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return engine, SessionLocal


def load_bug_ids_from_jsonl(file_path: str) -> Set[str]:
    """从JSONL文件中加载所有BUG ID"""
    bug_ids = set()
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return bug_ids
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    data = json.loads(line)
                    bug_id = data.get('ID')
                    if bug_id:
                        bug_ids.add(str(bug_id))
                    else:
                        print(f"⚠️  第{line_num}行缺少ID字段")
                except json.JSONDecodeError as e:
                    print(f"⚠️  第{line_num}行JSON解析失败: {e}")
                    continue
                    
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return set()
    
    print(f"📊 从文件中读取到 {len(bug_ids)} 个BUG ID")
    return bug_ids


def preview_deletion(session, bug_ids: Set[str]):
    """预览删除操作"""
    print("=" * 60)
    print("删除预览")
    print("=" * 60)
    
    # 查询匹配的主记录
    matching_bugs = session.query(BugEvaluation).filter(
        BugEvaluation.bug_id.in_(bug_ids)
    ).all()
    
    print(f"🔍 数据库中找到 {len(matching_bugs)} 条匹配的主记录")
    
    if not matching_bugs:
        print("✅ 数据库中没有匹配的记录，无需删除")
        return False
    
    # 统计关联记录
    matching_bug_evaluation_ids = [bug.id for bug in matching_bugs]
    
    title_count = session.query(TitleEvaluation).filter(
        TitleEvaluation.bug_evaluation_id.in_(matching_bug_evaluation_ids)
    ).count()
    
    desc_count = session.query(DescriptionEvaluation).filter(
        DescriptionEvaluation.bug_evaluation_id.in_(matching_bug_evaluation_ids)
    ).count()
    
    suggest_count = session.query(SuggestEvaluation).filter(
        SuggestEvaluation.bug_evaluation_id.in_(matching_bug_evaluation_ids)
    ).count()
    
    # 显示删除预览
    print(f"📋 主记录 (bug_evaluations): {len(matching_bugs)} 条")
    print(f"📝 标题评估 (title_evaluations): {title_count} 条")
    print(f"📄 描述评估 (description_evaluations): {desc_count} 条")
    print(f"💡 建议评估 (suggest_evaluations): {suggest_count} 条")
    print(f"📊 总计: {len(matching_bugs) + title_count + desc_count + suggest_count} 条记录")
    
    # 显示部分匹配记录详情
    print("\n匹配记录详情 (显示前5条):")
    for i, bug in enumerate(matching_bugs[:5]):
        print(f"{i+1}. BUG ID: {bug.bug_id}")
        print(f"   标题: {bug.title[:50]}{'...' if len(bug.title) > 50 else ''}")
        print(f"   创建人: {bug.creator}")
        print(f"   状态: {bug.status}")
        print()
    
    if len(matching_bugs) > 5:
        print(f"... 还有 {len(matching_bugs) - 5} 条记录")
    
    return True


def execute_deletion(session, bug_ids: Set[str]) -> int:
    """执行删除操作"""
    try:
        # 首先获取要删除的主记录ID
        bug_evaluations = session.query(BugEvaluation).filter(
            BugEvaluation.bug_id.in_(bug_ids)
        ).all()

        if not bug_evaluations:
            print("❌ 没有找到要删除的记录")
            return 0

        bug_evaluation_ids = [bug.id for bug in bug_evaluations]
        print(f"🔍 找到 {len(bug_evaluation_ids)} 条主记录需要删除")

        # 按照外键依赖顺序删除：先删除子表，再删除主表

        # 1. 删除建议评估记录
        suggest_deleted = session.query(SuggestEvaluation).filter(
            SuggestEvaluation.bug_evaluation_id.in_(bug_evaluation_ids)
        ).delete(synchronize_session=False)
        print(f"🗑️  删除建议评估记录: {suggest_deleted} 条")

        # 2. 删除描述评估记录
        desc_deleted = session.query(DescriptionEvaluation).filter(
            DescriptionEvaluation.bug_evaluation_id.in_(bug_evaluation_ids)
        ).delete(synchronize_session=False)
        print(f"🗑️  删除描述评估记录: {desc_deleted} 条")

        # 3. 删除标题评估记录
        title_deleted = session.query(TitleEvaluation).filter(
            TitleEvaluation.bug_evaluation_id.in_(bug_evaluation_ids)
        ).delete(synchronize_session=False)
        print(f"🗑️  删除标题评估记录: {title_deleted} 条")

        # 4. 最后删除主记录
        main_deleted = session.query(BugEvaluation).filter(
            BugEvaluation.bug_id.in_(bug_ids)
        ).delete(synchronize_session=False)
        print(f"🗑️  删除主评估记录: {main_deleted} 条")

        # 提交事务
        session.commit()

        total_deleted = suggest_deleted + desc_deleted + title_deleted + main_deleted
        print(f"✅ 成功删除总计 {total_deleted} 条记录")

        return main_deleted

    except Exception as e:
        # 回滚事务
        session.rollback()
        print(f"❌ 删除操作失败，已回滚: {e}")
        return 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='删除BUG评估数据')
    parser.add_argument('--preview', action='store_true', help='只预览，不执行删除')
    parser.add_argument('--force', action='store_true', help='强制删除，不需要确认')
    args = parser.parse_args()
    
    print("=" * 60)
    print("BUG评估数据删除脚本")
    print("=" * 60)
    
    # JSONL文件路径
    jsonl_file_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'app', 'bug_data', 'bug_evaluation_data.jsonl'
    )
    
    print(f"📁 读取文件: {jsonl_file_path}")
    
    # 1. 加载BUG ID
    bug_ids = load_bug_ids_from_jsonl(jsonl_file_path)
    if not bug_ids:
        print("❌ 没有加载到任何BUG ID，退出程序")
        return
    
    # 2. 创建数据库连接
    try:
        engine, SessionLocal = create_db_connection()
        session = SessionLocal()
        print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    try:
        # 3. 预览删除内容
        has_records = preview_deletion(session, bug_ids)
        
        if not has_records:
            return
        
        # 4. 如果只是预览，则退出
        if args.preview:
            print("\n💡 这只是预览，没有执行任何删除操作")
            return
        
        # 5. 确认删除操作
        if not args.force:
            print("\n" + "=" * 60)
            print("⚠️  警告: 即将执行删除操作！")
            print("⚠️  此操作不可逆，请确认！")
            print("=" * 60)
            confirm = input("确认删除吗？(输入 'DELETE' 确认): ").strip()
            if confirm != 'DELETE':
                print("❌ 用户取消删除操作")
                return
        
        # 6. 执行删除
        print("\n开始删除操作...")
        deleted_count = execute_deletion(session, bug_ids)
        
        if deleted_count > 0:
            print(f"\n🎉 删除操作完成，共删除 {deleted_count} 条记录")
        else:
            print("\n❌ 删除操作失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
    finally:
        # 关闭数据库会话
        session.close()


if __name__ == "__main__":
    main()
