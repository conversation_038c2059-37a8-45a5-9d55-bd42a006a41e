# BugConform 项目 Makefile

.PHONY: help install install-dev upgrade check freeze security clean test run

# 默认目标
help:
	@echo "BugConform 项目管理命令:"
	@echo ""
	@echo "依赖管理:"
	@echo "  install      - 安装生产环境依赖"
	@echo "  install-dev  - 安装开发环境依赖"
	@echo "  upgrade      - 升级所有依赖"
	@echo "  check        - 检查过时的依赖"
	@echo "  check-deps   - 检查依赖是否正确安装"
	@echo "  freeze       - 生成依赖快照"
	@echo "  security     - 安全漏洞检查"
	@echo ""
	@echo "开发工具:"
	@echo "  clean        - 清理缓存和临时文件"
	@echo "  test         - 运行测试套件"
	@echo "  format       - 格式化代码"
	@echo "  lint         - 代码质量检查"
	@echo ""
	@echo "应用管理:"
	@echo "  run          - 启动应用"
	@echo "  run-dev      - 开发模式启动应用"

# 依赖管理
install:
	@echo "🔄 安装生产环境依赖..."
	@python3 -m pip install -v -r requirements.txt -i https://pypi.org/simple/

install-dev:
	@echo "🔄 安装开发环境依赖..."
	@python3 -m pip install -r requirements-dev.txt -i https://pypi.org/simple/

upgrade:
	@echo "🔄 升级依赖..."
	@python3 -m pip install --upgrade -r requirements.txt -i https://pypi.org/simple/

check:
	@echo "🔍 检查过时的依赖..."
	pip list --outdated

check-deps:
	@echo "🔍 检查依赖安装状态..."
	python3 scripts/check_dependencies.py

freeze:
	@echo "📸 生成依赖快照..."
	pip freeze > requirements-freeze.txt
	@echo "✅ 依赖快照已保存到 requirements-freeze.txt"

security:
	@echo "🔒 检查安全漏洞..."
	@pip show safety > /dev/null 2>&1 || pip install safety
	safety check

# 开发工具
clean:
	@echo "🧹 清理缓存和临时文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf .pytest_cache/ 2>/dev/null || true
	rm -rf .coverage 2>/dev/null || true
	@echo "✅ 清理完成"

test:
	@echo "🧪 运行测试套件..."
	cd ../tests && python3 run_tests.py

format:
	@echo "🎨 格式化代码..."
	@command -v black >/dev/null 2>&1 || { echo "请先安装开发依赖: make install-dev"; exit 1; }
	black app/ scripts/
	isort app/ scripts/

lint:
	@echo "🔍 代码质量检查..."
	@command -v flake8 >/dev/null 2>&1 || { echo "请先安装开发依赖: make install-dev"; exit 1; }
	flake8 app/ scripts/
	mypy app/ --ignore-missing-imports

# 应用管理
run:
	@echo "🚀 启动应用..."
	python3 main.py

run-dev:
	@echo "🚀 开发模式启动应用..."
	uvicorn main:app --host 0.0.0.0 --port 8080 --reload

# 组合命令
setup: install-dev check-deps
	@echo "🎉 开发环境设置完成！"

ci: install check-deps security test lint
	@echo "🎉 CI检查通过！"
