from backend.app.scripts.trag.trag_manager import TRAGManager
from backend.app.config.config import TRAG_CASE_COLL, TRAG_NAMESPACE, TRAG_TOKEN
from backend.app.utils.logger_util import logger
import argparse
import sys
import json
from datetime import datetime, timedelta


def main():
    """
    主函数，支持命令行参数
    """
    parser = argparse.ArgumentParser(description='BUG数据导入TRAG向量数据库工具')
    parser.add_argument('--workspace-id', help='指定工作空间ID')
    parser.add_argument('--workspace-ids', nargs='+', help='指定多个工作空间ID')
    parser.add_argument('--batch-size', type=int, default=50, help='批处理大小，默认50')
    parser.add_argument('--all', action='store_true', help='导入所有工作空间的BUG数据')

    # 新增时间参数
    parser.add_argument('--start-time', help='开始时间，格式：2024-01-01 或 2024-01-01 00:00:00')
    parser.add_argument('--end-time', help='结束时间，格式：2024-01-01 或 2024-01-01 23:59:59')
    parser.add_argument('--from-tapd', action='store_true', help='从TAPD获取BUG数据（而不是从数据库）')
    parser.add_argument('--past-days', type=int, help='获取过去多少天的BUG数据，例如：--past-days 30')

    args = parser.parse_args()

    try:
        # 创建TRAG管理器
        manager = TRAGManager(coll_code=TRAG_CASE_COLL, ns_code=TRAG_NAMESPACE, trag_token=TRAG_TOKEN)
        logger.info("TRAG管理器初始化成功")

        # 确定要处理的工作空间ID
        workspace_ids = None
        if args.workspace_id:
            workspace_ids = [args.workspace_id]
            logger.info(f"将处理单个工作空间: {args.workspace_id}")
        elif args.workspace_ids:
            workspace_ids = args.workspace_ids
            logger.info(f"将处理多个工作空间: {workspace_ids}")
        elif args.all:
            logger.info("将处理所有工作空间的BUG数据")
        else:
            # 默认处理指定工作空间
            workspace_ids = ['20375472']  # 默认工作空间ID
            logger.info(f"使用默认工作空间: {workspace_ids}")

        # 处理时间参数
        start_time = None
        end_time = None

        if args.past_days:
            # 如果指定了过去天数，计算时间范围
            end_datetime = datetime.now()
            start_datetime = end_datetime - timedelta(days=args.past_days)
            start_time = start_datetime.strftime("%Y-%m-%d 00:00:00")
            end_time = end_datetime.strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"将获取过去{args.past_days}天的BUG数据: {start_time} ~ {end_time}")
        else:
            start_time = args.start_time
            end_time = args.end_time
            if start_time or end_time:
                logger.info(f"将获取指定时间范围的BUG数据: {start_time} ~ {end_time}")

        # 根据参数选择导入方式
        if args.from_tapd or start_time or end_time or args.past_days:
            # 从TAPD获取BUG数据
            logger.info("开始从TAPD获取BUG数据并导入到TRAG向量数据库...")
            result = manager.import_bugs_by_time_range(
                start_time=start_time,
                end_time=end_time,
                workspace_ids=workspace_ids,
                batch_size=args.batch_size
            )

            if result.get("success"):
                logger.info(f"从TAPD导入BUG数据完成！成功导入: {result.get('total_imported')} 个文档")
            else:
                logger.error(f"从TAPD导入BUG数据失败: {result.get('error')}")
        else:
            # 从数据库导入BUG数据（原有逻辑）
            logger.info("开始从数据库导入BUG数据到TRAG向量数据库...")
            manager.import_bugs_from_dataset(
                workspace_ids=workspace_ids,
                batch_size=args.batch_size
            )
            logger.info("从数据库导入BUG数据完成！")

    except Exception as e:
        logger.error(f"导入BUG数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def test_search():
    """
    测试搜索功能
    """
    try:
        manager = TRAGManager(coll_code=TRAG_CASE_COLL, ns_code=TRAG_NAMESPACE, trag_token=TRAG_TOKEN)

        # 测试搜索
        test_queries = [
            "健康管理助手模块有哪些历史BUG",
            "登录相关的缺陷",
            "界面显示问题",
            "数据同步错误"
        ]

        for query in test_queries:
            print(f"\n搜索查询: {query}")
            print("-" * 50)

            results = manager.search_bugs(query, limit=5)

            if results:
                for i, result in enumerate(results, 1):
                    try:
                        print(f"{i}. 相似度: {getattr(result, 'score', 'N/A')}")
                        print(f"   文档ID: {getattr(result, 'id', 'N/A')}")

                        doc_content = getattr(result, 'doc', '{}')
                        if isinstance(doc_content, str):
                            doc_data = json.loads(doc_content)
                        else:
                            doc_data = doc_content  # 已是 dict 或 pydantic.BaseModel

                        # 如果是 Pydantic 模型对象，转换为字典
                        if hasattr(doc_data, 'dict'):
                            doc_data = doc_data.dict()

                        print(f"   标题: {doc_data.get('title', 'N/A')}")
                        print(f"   模块: {doc_data.get('module', 'N/A')}")
                        print(f"   状态: {doc_data.get('status', 'N/A')}")
                    except Exception as inner_e:
                        print(f"   解析结果失败: {str(inner_e)}")
                        print(f"   原始内容: {str(result)[:100]}...")
                    print()
            else:
                print("未找到相关结果")

    except Exception as e:
        logger.error(f"搜索测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 测试模式
        test_search()
    else:
        # 导入模式
        main()


