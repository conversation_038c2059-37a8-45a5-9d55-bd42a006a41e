from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from backend.app.utils.logger_util import logger
from sqlalchemy import and_
from sqlalchemy.orm import Session
# 创建基类
Base = declarative_base()

from backend.app.models.bug import BugEvaluation, TitleEvaluation, DescriptionEvaluation, SuggestEvaluation

# 全局变量初始化为 None
_engine = None
SessionLocal = None
        

def init_db():
    """延迟初始化数据库连接，在需要时显式调用"""
    global _engine, SessionLocal

    # 从环境变量获取数据库配置
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_HOST = os.getenv('DB_HOST')
    DB_PORT = os.getenv('DB_PORT')
    DB_NAME = os.getenv('DB_NAME')

    if not all([DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]):
        raise ValueError("环境变量未完全设置，无法初始化数据库连接。")

    # 构建数据库URL
    SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

    # 创建数据库引擎和会话工厂
    _engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=3600,
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=_engine)

def get_engine():
    if _engine is None:
        raise RuntimeError("Engine 尚未初始化，请先调用 init_db()。")
    return _engine


# 获取数据库会话的依赖函数
def get_db():
    if SessionLocal is None:
        raise RuntimeError("数据库未初始化，请先调用 init_db()。")
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
init_db()
engine = get_engine()
Base.metadata.create_all(bind=engine)