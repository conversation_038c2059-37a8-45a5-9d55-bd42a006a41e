from fastapi import APIRouter
from app.handlers import handlers
from app.routers import evaluation_api

router = APIRouter()

# 健康检查
#router.post("/check-bugs")(handlers.check_bugs_message)

# 示例路由
router.get("/hello")(handlers.hello)

# 包含评估查询API
router.include_router(evaluation_api.router, prefix="/evaluation", tags=["evaluation"])

router.get("/callback")(handlers.callback)

router.post("/callback")(handlers.receive_message)
