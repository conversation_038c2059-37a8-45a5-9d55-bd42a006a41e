"""
BUG评估结果查询API
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from backend.app.service.bug_evaluation_service import (
    get_bug_evaluation_by_id,
    get_bug_evaluations_by_bug_id,
    get_daily_bug_interception_stats
)
from backend.app.utils.logger_util import logger

router = APIRouter()

class EvaluationResponse(BaseModel):
    """评估结果响应模型"""
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None

class EvaluationListResponse(BaseModel):
    """评估结果列表响应模型"""
    status: str
    message: str
    data: List[Dict[str, Any]] = []


@router.get("/evaluations/stats", response_model=Dict[str, Any])
async def get_evaluation_stats(
    workspace_id: Optional[str] = Query(None, description="工作空间ID"),
    days: int = Query(7, description="统计天数", ge=1, le=365)
):
    """
    获取评估统计信息
    
    Args:
        workspace_id: 工作空间ID（可选）
        days: 统计天数（默认7天）
    
    Returns:
        评估统计数据
    """
    try:
        logger.info(f"获取评估统计信息，工作空间: {workspace_id}, 天数: {days}")
        
        # 这里可以添加更复杂的统计查询逻辑
        # 目前返回基本的响应结构
        stats = {
            "total_evaluations": 0,
            "passed_evaluations": 0,
            "failed_evaluations": 0,
            "pass_rate": 0.0,
            "period_days": days,
            "workspace_id": workspace_id
        }
        
        return {
            "status": "success",
            "message": "获取统计信息成功",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取评估统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/daily-interception-stats", response_model=Dict[str, Any])
async def get_daily_interception_stats(
    date: Optional[str] = Query(None, description="统计日期，格式YYYY-MM-DD，默认为今天")
):
    """
    获取指定日期的BUG拦截统计数据

    Args:
        date: 统计日期，格式YYYY-MM-DD，默认为今天

    Returns:
        包含拦截统计数据的字典，包括：
        - 总拦截BUG数量
        - 详细描述误报数量及百分比
        - 详细描述建议无效数量及百分比
        - 标题误报数量及百分比
        - 标题建议无效数量及百分比
    """
    try:
        logger.info(f"获取BUG拦截统计数据，日期: {date or '今天'}")

        # 调用服务层获取统计数据
        stats_data = await get_daily_bug_interception_stats(date)
    
        # 检查是否有错误
        if "error" in stats_data:
            raise HTTPException(status_code=400, detail=stats_data["error"])

        return {
            "status": "success",
            "message": "获取BUG拦截统计数据成功",
            "data": stats_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取BUG拦截统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
