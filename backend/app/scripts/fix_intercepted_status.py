from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_
from backend.app.config.config import YIBAO_ROBOT_WEB_HOOK
from backend.app.database.database import get_db
from backend.app.models.bug import BugEvaluation
from backend.app.utils.logger_util import logger
from backend.app.utils.tapd import tap_client
def update_is_intercepted_status():
    """
    遍历所有 BugEvaluation，如果其 title 和 description 评估都通过，则 is_intercepted = False，否则为 True
    """
    try:
        db_gen = get_db()
        db = next(db_gen)

        try:
            all_evaluations = db.query(BugEvaluation).all()

            for bug in all_evaluations:
                title_passed = all(t.passed for t in bug.title_evaluations) if bug.title_evaluations else False
                desc_passed = all(d.passed for d in bug.description_evaluations) if bug.description_evaluations else False

                should_intercept = not (title_passed and desc_passed)

                if bug.is_intercepted != should_intercept:
                    logger.info(f"更新 BUG {bug.bug_id}: is_intercepted = {should_intercept}")
                    bug.is_intercepted = should_intercept
                    db.add(bug)

            db.commit()
            logger.info("✅ 所有 BUG 的 is_intercepted 状态更新完成")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"❌ 更新 BUG 拦截状态失败: {str(e)}")

update_is_intercepted_status()