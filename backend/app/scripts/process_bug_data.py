from multiprocessing import process
import os
import json
import asyncio
from pathlib import Path
import sys
import time
# 添加项目根目录到Python路径，以便导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from backend.app.handlers.handlers import check_bug_data


async def process_jsonl_files():
    """处理bug_data目录中的jsonl文件，提取每条json数据，剔除markdown_content，然后传入check_bug_data函数，并覆盖写回"""
    bug_data_dir = Path(os.path.dirname(__file__), "../bug_data")

    if not bug_data_dir.exists():
        print(f"目录不存在: {bug_data_dir}")
        return

    # jsonl_files = list(bug_data_dir.glob("*.jsonl"))
    jsonl_files = ["backend/app/bug_data/bug_evaluation_data.jsonl"]
    if not jsonl_files:
        print(f"在 {bug_data_dir} 中没有找到jsonl文件")
        return

    print(f"找到 {len(jsonl_files)} 个jsonl文件")
    not_passed_bug_titles = []
    for jsonl_file in jsonl_files:
        print(f"处理文件: {jsonl_file}")
        processed_count = 0
        updated_lines = []

        try:
            with open(jsonl_file, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        bug_data = json.loads(line)
                        processed_count += 1
                        if processed_count < 21:
                            continue
                        # 提取 workspace_id
                        workspace_id = None
                        if "需求链接" in bug_data:
                            link = bug_data.get("需求链接", "")
                            if link and "/tapd_fe/" in link:
                                workspace_id = link.split("/tapd_fe/")[1].split("/")[0]

                        # 剔除字段
                        bug_data.pop("markdown_content", None)

                        # 处理数据
                        bug_data = await check_bug_data(bug_data, workspace_id)
                        time.sleep(5)
                        updated_lines.append(json.dumps(bug_data, ensure_ascii=False))
                        passed = bug_data.get("passed", True)
                        if not passed:
                            not_passed_bug_titles.append(bug_data.get("标题", "未知标题"))
                        if processed_count == 50:
                            return 
                        if processed_count % 100 == 0:
                            print(f"已处理 {processed_count} 条数据")

                    except json.JSONDecodeError:
                        print(f"文件 {jsonl_file} 的第 {line_num} 行包含无效的JSON数据")
                    except Exception as e:
                        print(f"处理文件 {jsonl_file} 的第 {line_num} 行时出错: {str(e)}")

            # 覆盖写入文件
            with open(jsonl_file, "w", encoding="utf-8") as f:
                f.write("\n".join(updated_lines) + "\n")

        except Exception as e:
            print(f"处理文件 {jsonl_file} 时出错: {str(e)}")

        print(f"文件 {jsonl_file} 处理完成，共处理 {processed_count} 条数据")
    print(json.dumps(not_passed_bug_titles, ensure_ascii=False, indent=4))

if __name__ == "__main__":
    asyncio.run(process_jsonl_files())