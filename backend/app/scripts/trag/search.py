import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()


def search(rag_code="is-ee70b4ff", ns_code="ns-74dfc49c", coll_code=None, docs=None, limit=5):
    if docs is None:
        docs = [""]
    if coll_code is None:
        coll_code = [""]
    url = "http://api.trag.woa.com/v1/application/search/collection"

    headers = {
        'Content-Type': 'application/json; charset=utf-8',
        'Authorization': f'Bearer {os.getenv("TRAG_TOKEN")}'
    }

    data = {
        "ragCode": rag_code,
        "namespaceCode": ns_code,
        "collectionCodes": coll_code,
        "embeddingModel":"public-bge-m3",
        "docs": docs,
        "limit": limit
    }

    response = requests.post(url, headers=headers, json=data)
    print(response)
    return response

if __name__ == '__main__':

    response = search(rag_code="is-ee70b4ff", ns_code="ns-74dfc49c", coll_code=["col-8d937358"], docs=["与AIGC健康问问模块 相关的用例"], limit=5)
    print(response.json())

