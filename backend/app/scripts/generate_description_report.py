import os
import json
import asyncio
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID
import pandas as pd
from pathlib import Path
import sys
import time
import backend.app.database.database as database
# 添加项目根目录到Python路径，以便导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from backend.app.handlers.handlers import check_bug_data

def save_to_excel(data, output_file):
    """将数据写入Excel文件"""
    df = pd.DataFrame(data)
    df.to_excel(output_file, index=False)

async def generate_bug_report():
    """处理bug_data目录中的jsonl文件，提取每条json数据，调用check_bug_data函数，并生成Excel报告"""
    bug_data_dir = Path(os.path.dirname(__file__), "../bug_data")
    output_dir = Path(os.path.dirname(__file__), "../reports")
    output_file = output_dir / "bug_report.xlsx"

    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)

    if not bug_data_dir.exists():
        print(f"目录不存在: {bug_data_dir}")
        return

    jsonl_file = "backend/app/bug_data/bug_evaluation_data.jsonl"
    #jsonl_file = "backend/app/bug_data/bug_evalution_title.jsonl"
    if not os.path.exists(jsonl_file):
        print(f"文件不存在: {jsonl_file}")
        return

    print(f"处理文件: {jsonl_file}")
    processed_count = 0
    excel_data = []

    try:
        with open(jsonl_file, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                try:
                    bug_data = json.loads(line)
                    processed_count += 1

                    # 提取 workspace_id
                    workspace_id = BUG_EVALUATION_WORKSPACE_ID
                    if "需求链接" in bug_data:
                        link = bug_data.get("需求链接", "")
                        if link and "/tapd_fe/" in link:
                            workspace_id = link.split("/tapd_fe/")[1].split("/")[0]

                    # 保存原始详细描述
                    original_description = bug_data.get("详细描述", "")

                    # 调用评估方法
                    result_data = await check_bug_data(bug_data, workspace_id)
                    time.sleep(1)  # 控制调用频率

                    # 构建缺陷链接
                    bug_id = bug_data.get("ID", "")
                    bug_link = ""
                    if workspace_id:
                        bug_link = f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bug_id}"
                    else:
                        bug_link = bug_data.get("需求链接", "")

                    # 提取评分和反馈信息
                    dimension_scores = result_data.get("纬度评分", "")
                    feedback = result_data.get("feedback", "")
                    suggestion = result_data.get("suggest", "")

                    # 添加数据到列表
                    excel_data.append({
                        "缺陷标题": bug_data.get("标题", ""),
                        "缺陷链接": bug_link,
                        "创建人": bug_data.get("创建人", ""),
                        "原始详细描述": original_description,
                        "详细描述评分": dimension_scores,
                        "评分反馈": feedback,
                        "建议": suggestion
                    })

                    # 每10条保存一次Excel
                    if processed_count % 10 == 0:
                        print(f"已处理 {processed_count} 条数据，保存中...")
                        save_to_excel(excel_data, output_file)
                    if processed_count == 50:
                        return 
                except json.JSONDecodeError:
                    print(f"第 {line_num} 行包含无效的JSON数据")
                except Exception as e:
                    print(f"第 {line_num} 行处理出错: {str(e)}")
                    import traceback
                    traceback.print_exc()

        # 处理完成后最终写入一次Excel
        if excel_data:
            save_to_excel(excel_data, output_file)
            print(f"报告已生成: {output_file}")
        else:
            print("没有数据生成报告")

    except Exception as e:
        print(f"处理文件 {jsonl_file} 时出错: {str(e)}")
        import traceback
        traceback.print_exc()

    print(f"文件 {jsonl_file} 处理完成，共处理 {processed_count} 条数据")

if __name__ == "__main__":
    database.init_db()
    asyncio.run(generate_bug_report())
