from typing import List, Dict
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_
from backend.app.config.config import YIBAO_ROBOT_WEB_HOOK
from backend.app.database.database import get_db
from backend.app.models.bug import BugEvaluation
from backend.app.utils.logger_util import logger
from backend.app.utils.tapd import tap_client

def get_bugs_by_workspace_and_time_range(workspace_id: str, start_time: str, end_time: str = None) -> List[Dict]:
    """
    查询指定 workspace_id 和时间范围内的 BUG 记录，包含描述字段

    Args:
        workspace_id: 工作空间 ID
        start_time: 开始时间，格式：'2024-01-01' 或 '2024-01-01 09:00:00'
        end_time: 结束时间，格式：'2024-01-01' 或 '2024-01-01 18:00:00'

    Returns:
        BUG 记录列表，包含 bug_id、title、creator、description、evaluated_at、bug_link
    """
    try:
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # 解析时间
            start_datetime = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S" if len(start_time) > 10 else "%Y-%m-%d")
            if len(start_time) == 10:
                start_datetime = start_datetime.replace(hour=0, minute=0, second=0, microsecond=0)

            if end_time:
                end_datetime = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S" if len(end_time) > 10 else "%Y-%m-%d")
                if len(end_time) == 10:
                    end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
            else:
                end_datetime = datetime.now()

            query = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.workspace_id == workspace_id,
                    BugEvaluation.created_at >= start_datetime,
                    BugEvaluation.created_at <= end_datetime
                )
            ).order_by(BugEvaluation.created_at.asc())

            bug_list = query.all()

            results = []
            for bug in bug_list:
                bug_link = f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bug.bug_id}"
                results.append({
                    "bug_id": bug.bug_id,
                    "title": bug.title or "未知标题",
                    "creator": bug.creator or "未知创建人",
                    "description": bug.description or "暂无描述",
                    "evaluated_at": bug.evaluated_at.strftime("%Y-%m-%d %H:%M:%S") if bug.evaluated_at else None,
                    "bug_link": bug_link
                })

            logger.info(f"查询到 {len(results)} 个 BUG 记录")
            return results

        finally:
            db.close()

    except Exception as e:
        logger.error(f"查询 BUG 记录失败: {str(e)}")
        return []
    

if __name__ == "__main__":
    # 示例调用
    workspace_id = "20426960"
    start_time = "2025-07-16 00:00:00"
    end_time = "2025-07-18 23:59:59"
    bugs = get_bugs_by_workspace_and_time_range(workspace_id, start_time, end_time)
    bug_ids = []
    filter_bugs = []
    for bug in bugs:
        if bug["bug_id"] not in bug_ids:
            bug_ids.append(bug["bug_id"])
            filter_bugs.append(bug)
    for bug in filter_bugs:
        bug_details = tap_client.get_bug_by_id(workspace_id, bug["bug_id"])
        if bug_details == {}:
            print(f"BUG {bug['bug_id']} 不存在或已被删除")
        else:
            print('标题：' + bug_details['title'] + " " + '创建人：' + bug_details['reporter'] + " " + 'id：' + bug_details['id'] + " " + '链接：' + f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bug_details['id']}\n")
