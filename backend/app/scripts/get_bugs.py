import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Set
import sys
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '../.env'))
WORKSPACE_ID = os.getenv('WORKSPACE_ID')
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from backend.app.utils.tapd import tap_client
from backend.app.database.database import SessionLocal
from openpyxl import Workbook, load_workbook
from glob import glob
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
CHUNK_SIZE = 5000        # 用于拆分文件的块大小
SAVE_BATCH = 1000        # 每累计多少条数据保存一次
EXCEL_DIR = "bugs"       # 存放文件的目录
PATTERN = os.path.join(EXCEL_DIR, "backend/bugs_*-[0-9]*.xlsx")


def _load_existing_ids(pattern: str = PATTERN) -> Set[str]:
    """扫描已有的 Excel，加载所有已处理过的缺陷 ID"""
    existing = set()
    for path in glob(pattern):
        try:
            df = pd.read_excel(path, engine='openpyxl')
            if "ID" in df.columns:
                existing.update(df["ID"].dropna().astype(str).tolist())
        except Exception:
            continue
    return existing


def get_all_bugs(workspace_id: str) -> List[Dict]:
    """
    拉取所有缺陷并保存到分块 Excel 文件中，
    每累计 SAVE_BATCH 条才做一次磁盘写入。
    返回本次新拉取的缺陷列表。
    """
    os.makedirs(EXCEL_DIR, exist_ok=True)
    existing_ids = _load_existing_ids()
    new_bugs: List[Dict] = []

    # 计算现有数据已经用了多少完整块
    offset_chunks = len(existing_ids) // CHUNK_SIZE
    page = 1
    total_new = 0

    # 缓冲区：chunk_idx -> list of rows
    buffers: Dict[int, List[List]] = {}

    while True:
        bugs_page = tap_client.get_bugs(workspace_id, page=page, limit=200)
        if not bugs_page:
            break

        # 取关联故事信息
        ids = [b["Bug"]["id"] for b in bugs_page if b.get("Bug", {}).get("id")]
        story_refs = tap_client.get_bug_related_story(workspace_id, ids)
        story_map = {int(r["bug_id"]): r.get("story_id") for r in story_refs}
        story_ids = [sid for sid in story_map.values() if sid]
        stories = tap_client.get_stories_by_ids(workspace_id, story_ids)
        story_lookup = {s["Story"]["id"]: s["Story"] for s in stories}

        for item in bugs_page:
            raw = item.get("Bug", {})
            bid = str(raw.get("id", ""))
            if not bid or bid in existing_ids:
                continue

            existing_ids.add(bid)
            data = tap_client.process_bug_field(raw, workspace_id)
            data["ID"] = bid
            data["缺陷链接"] = (
                f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bid}"
            )

            sid = story_map.get(int(raw["id"]), None)
            if sid and sid in story_lookup:
                story = story_lookup[sid]
                data["需求名称"] = story.get("name", "")
                data["需求链接"] = (
                    f"https://tapd.woa.com/tapd_fe/{workspace_id}/story/detail/{sid}"
                )

            # 计算写到哪个 chunk（块）
            chunk_idx = offset_chunks + (total_new // CHUNK_SIZE)
            buffers.setdefault(chunk_idx, []).append(list(data.values()))

            new_bugs.append(data)
            total_new += 1

            # 如果本 chunk 累积到 SAVE_BATCH 条，就写入并保存
            if len(buffers[chunk_idx]) >= SAVE_BATCH:
                _flush_chunk(chunk_idx, list(data.keys()), buffers[chunk_idx])
                buffers[chunk_idx].clear()

        page += 1
        # 根据需求决定是否 sleep
        # time.sleep(1)

    # 循环结束后，flush 所有剩余不足 SAVE_BATCH 的部分
    for idx, rows in buffers.items():
        if rows:
            # keys 可能不一致，取第一条的 keys
            headers = list(new_bugs[0].keys()) if new_bugs else rows[0]
            _flush_chunk(idx, headers, rows)

    return new_bugs


def _flush_chunk(chunk_idx: int, headers: List[str], rows: List[List]):
    """
    将给定 chunk 的缓冲行写到对应的 Excel 文件并保存（追加方式）
    """
    start = chunk_idx * CHUNK_SIZE + 1
    end = (chunk_idx + 1) * CHUNK_SIZE
    fname = os.path.join(EXCEL_DIR, f"bugs_{start}-{end}.xlsx")

    if os.path.exists(fname):
        # 加载已有文件并追加内容
        wb = load_workbook(fname)
        ws = wb.active
    else:
        wb = Workbook()
        ws = wb.active
        ws.append(headers)

    # 写入前过滤非法字符
    for row in rows:
        clean_row = []
        for cell in row:
            if isinstance(cell, str):
                cell = ILLEGAL_CHARACTERS_RE.sub("", cell)
            clean_row.append(cell)
        ws.append(clean_row)

    wb.save(fname)


if __name__ == "__main__":
    get_all_bugs(workspace_id=20375472)