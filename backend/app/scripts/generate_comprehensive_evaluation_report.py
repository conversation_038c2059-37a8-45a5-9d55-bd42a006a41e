import os
import json
import asyncio
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID
import pandas as pd
from pathlib import Path
import sys
import time
import backend.app.database.database as database
# 添加项目根目录到Python路径，以便导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from backend.app.handlers.handlers import check_bug_data

def save_to_excel(data, output_file):
    """将数据写入Excel文件"""
    df = pd.DataFrame(data)
    df.to_excel(output_file, index=False)

async def generate_comprehensive_bug_evaluation_report():
    """处理bug_data目录中的jsonl文件，提取每条json数据，调用四个维度的评估函数，并生成综合Excel报告"""
    bug_data_dir = Path(os.path.dirname(__file__), "../bug_data")
    output_dir = Path(os.path.dirname(__file__), "../reports")
    output_file = output_dir / "综合BUG评估报告.xlsx"

    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)

    if not bug_data_dir.exists():
        print(f"目录不存在: {bug_data_dir}")
        return

    # 使用最新的评估数据文件
    jsonl_file = "backend/app/bug_data/bug_evaluation_data.jsonl"
    if not os.path.exists(jsonl_file):
        print(f"文件不存在: {jsonl_file}")
        return

    print(f"处理文件: {jsonl_file}")
    processed_count = 0
    excel_data = []

    try:
        with open(jsonl_file, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                try:
                    bug_data = json.loads(line)
                    processed_count += 1

                    # 提取 workspace_id
                    workspace_id = BUG_EVALUATION_WORKSPACE_ID
                    if "需求链接" in bug_data:
                        link = bug_data.get("需求链接", "")
                        if link and "/tapd_fe/" in link:
                            workspace_id = link.split("/tapd_fe/")[1].split("/")[0]

                    # 调用评估方法
                    result_data = await check_bug_data(bug_data, workspace_id)
                    time.sleep(1)  # 控制调用频率

                    # 构建缺陷链接
                    bug_id = bug_data.get("ID", "")
                    bug_link = ""
                    if workspace_id:
                        bug_link = f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bug_id}"
                    else:
                        bug_link = bug_data.get("需求链接", "")

                    excel_data.append({
                        # 基本信息
                        "缺陷标题": bug_data.get("标题", ""),
                        "缺陷链接": bug_link,
                        "创建人": bug_data.get("创建人", ""),
                        "状态": bug_data.get("状态", ""),
                        "模块": bug_data.get("模块", ""),
                        "原始详细描述": bug_data.get("详细描述", ""),
                        "原始优先级": bug_data.get("优先级", ""),
                        "原始严重程度": bug_data.get("严重程度", ""),

                        # 详细描述评估（纬度评分）
                        "详细描述是否通过": "是" if bug_data.get("是否通过", True) else "否",
                        "详细描述维度评分": json.dumps(bug_data.get("纬度评分", {}), ensure_ascii=False),
                        "详细描述反馈": bug_data.get("feedback", ""),
                        "详细描述建议": bug_data.get("suggest", ""),

                        # 标题评估
                        "标题是否通过": "是" if bug_data.get("标题是否通过", True) else "否",
                        "标题维度评分": json.dumps(bug_data.get("标题纬度评分", {}), ensure_ascii=False),
                        "标题反馈": bug_data.get("标题反馈", ""),
                        "标题建议": bug_data.get("标题建议", ""),
                        "标题思维过程阶段1": bug_data.get("标题阶段1思维过程", ""),
                        "标题思维过程阶段2": bug_data.get("标题阶段2思维过程", ""),
                        "标题必要元素": json.dumps(bug_data.get("标题需要要素", []), ensure_ascii=False),
                        "标题元素判断原因": bug_data.get("标题要素判断原因", ""),

                        # 优先级评估
                        "优先级建议值": bug_data.get("优先级建议", ""),
                        "优先级实际值": bug_data.get("优先级实际值", ""),
                        "优先级建议原因": bug_data.get("优先级建议原因", ""),

                        # 严重程度评估
                        "严重程度建议值": bug_data.get("严重程度建议", ""),
                        "严重程度实际值": bug_data.get("严重程度实际值", ""),
                        "严重程度建议原因": bug_data.get("严重程度建议原因", "")
                    })
                    # 每10条保存一次Excel
                    if processed_count % 1 == 0:
                        print(f"已处理 {processed_count} 条数据，保存中...")
                        save_to_excel(excel_data, output_file)
                    
                    # 限制处理数量用于测试
                    if processed_count == 50:
                        break
                        
                except json.JSONDecodeError:
                    print(f"第 {line_num} 行包含无效的JSON数据")
                except Exception as e:
                    print(f"第 {line_num} 行处理出错: {str(e)}")
                    import traceback
                    traceback.print_exc()

        # 处理完成后最终写入一次Excel
        if excel_data:
            save_to_excel(excel_data, output_file)
            print(f"综合评估报告已生成: {output_file}")
            print(f"报告包含以下评估维度：")
            print("- 详细描述评估（维度评分、反馈、建议）")
            print("- 标题评估（维度评分、反馈、建议、思维过程）")
            print("- 优先级评估（建议值、实际值、原因、反馈）")
            print("- 严重程度评估（建议值、实际值、原因、反馈）")
        else:
            print("没有数据生成报告")

    except Exception as e:
        print(f"处理文件 {jsonl_file} 时出错: {str(e)}")
        import traceback
        traceback.print_exc()

    print(f"文件 {jsonl_file} 处理完成，共处理 {processed_count} 条数据")

if __name__ == "__main__":
    database.init_db()
    asyncio.run(generate_comprehensive_bug_evaluation_report())
