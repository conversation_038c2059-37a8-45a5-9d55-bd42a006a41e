import os
import json
import sys
from datetime import datetime
from typing import List, Dict
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))
WORKSPACE_ID = os.getenv('WORKSPACE_IDS')

# 添加项目根目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from backend.app.utils.tapd import tap_client


def get_bug_evaluation_data(workspace_id: str, limit: int = 100) -> List[Dict]:
    """
    获取指定数量的 bug 数据，并处理成评测集格式
    
    Args:
        workspace_id: 工作区 ID
        limit: 获取的 bug 数量，默认为 100
        
    Returns:
        处理后的 bug 数据列表
    """
    # 获取 bug 数据
    page = 1
    total_bugs = 0
    all_bugs = []
    
    while total_bugs < limit:
        # 获取一页 bug 数据
        bugs_page = tap_client.get_bugs(workspace_id, page=page, limit=min(200, limit - total_bugs))
        if not bugs_page:
            break
            
        # 处理每个 bug
        for item in bugs_page:
            if total_bugs >= limit:
                break
                
            raw = item.get("Bug", {})
            bug_id = str(raw.get("id", ""))
            if not bug_id:
                continue
                
            # 获取完整的 bug 数据
            bug_data = tap_client.get_bug_all_pure_message(workspace_id, bug_id)
            if bug_data:
                all_bugs.append(bug_data)
                total_bugs += 1
                print(f"已处理 {total_bugs}/{limit} 条 bug 数据")
        
        page += 1
        if not bugs_page or len(bugs_page) < 200:
            break
    
    return all_bugs


def save_to_jsonl(bugs: List[Dict], output_dir: str = "../bug_data"):
    """
    将 bug 数据保存到 jsonl 文件
    
    Args:
        bugs: bug 数据列表
        output_dir: 输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 构造输出文件名
    output_file = os.path.join(output_dir, f"bug_evaluation_data.jsonl")
    
    # 写入文件
    with open(output_file, "w", encoding="utf-8") as f:
        for bug in bugs:
            f.write(json.dumps(bug, ensure_ascii=False) + "\n")
    
    print(f"已将 {len(bugs)} 条 bug 数据保存到 {output_file}")


def main():
    """
    主函数
    """
    if not WORKSPACE_ID:
        print("请在 .env 文件中设置 WORKSPACE_ID")
        return
        
    # 获取 bug 数据
    bugs = get_bug_evaluation_data(WORKSPACE_ID, limit=100)
    
    # 保存到文件
    if bugs:
        save_to_jsonl(bugs)
    else:
        print("未获取到 bug 数据")


if __name__ == "__main__":
    main()