import json
def get_hunyuan_version_prompt(bug_data: str, image_url) -> str:
    return f"""
# 背景 #
你是一名缺陷BUG领域的智能多模态图片解析专家，擅长分析用户提交的缺陷中图片表达的本质内容。

# 目的 #
你的目的是结合缺陷上下文，合理分析出该图片在缺陷上下文中想表达的本质意思。

# 输入信息 #
1. 以下是与该截图相关的缺陷信息（包含标题、模块、描述等），请将缺陷信息与截图内容结合，用自然语言总结页面的**重点内容**，尤其关注与缺陷相关的区域。
===== 
{json.dumps(bug_data, ensure_ascii=False, indent=4)}
=====

2. 以下是这次分析的图片的链接，你可以在缺陷详细描述字段中找到，请结合缺陷上下文，分析图片内容：
=====
{image_url}
=====

# 思维链 #
1. 请先分析缺陷上下文，理解缺陷的本质内容和用户意图。
2. 然后分析截图内容，提取出与缺陷相关的页面信息。
3. 最后结合缺陷的上下文，分析图片在缺陷中的位置，最后再提炼出页面的重点内容。

# 总结要求 #
1. 内容要求：
    a. 先输出自己对缺陷的理解，并弄清楚这个图片在缺陷中的位置。
    b. **如果截图中存在红框、高亮、箭头等标记**，请**优先描述这些标记区域的内容并说明使用红框、高亮、箭头标记**，并指出“页面中的标注区域”或“重点提示区域”包含了哪些信息。
    c. **结合缺陷描述内容**，重点提取与问题相关的页面区域和信息点。例如页面报错、字段遮挡、信息缺失、按钮无响应等。
    d. **如果截图中没有明显标记**，请提炼页面中的信息结构、显著字段和核心内容。无需列举所有字段，只需保留关键内容。
    e. **不要编造截图中不存在的内容**。如信息被截断或页面展示不完整，请在总结中说明该情况。
2. 输出格式：
    a. 输出为自然语言描述，**不要使用JSON格式**。
    b. 保持语言精简、专业，避免口语化或缩略语。
    c. **不要添加额外的解释或说明**，仅输出页面的重点内容。

# 📤 输出示例（仅输出 JSON，禁止任何解释性内容，不要参考示例具体信息）#
该截图所在位置为缺陷详细描述中的xxxx位置，结合缺陷的本质为xxx，截图红框重点标注区域页面xxx，结合缺陷上下文，可以得出该截图的重点内容为xxx。
"""
