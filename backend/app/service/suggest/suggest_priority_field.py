# file: backend/app/service/suggest/suggest_priority_field.py

from backend.app.service.suggest.field_evaluation_models import SuggestFieldEvaluation
from backend.app.service.suggest.field_evaluator import _evaluate_field_with_llm
from backend.app.utils.logger_util import logger

async def evaluate_suggest_priority_field(
    field_name: str,
    field_content: dict,
    workspace_id: str = ""
) -> SuggestFieldEvaluation:
    """
    使用混元模型评估“优先级”字段，返回通用的 SuggestFieldEvaluation。
    """
    try:
        # 统一调用公共方法，json_key 固定为 "suggest_priority"
        return await _evaluate_field_with_llm(field_name, field_content, json_key="suggested_priority", workspace_id=workspace_id)
    except Exception as e:
        logger.error(f"[Exception] evaluate_suggest_priority_field 出错: {str(e)}")
        import traceback; traceback.print_exc()
        return SuggestFieldEvaluation(
            field=field_name,
            suggested="",
            reason="",
            feedback=f"评估过程出错: {str(e)}"
        )

async def get_priority_suggestion(
    field: str,
    field_content: dict,
    workspace_id: str = ""
) -> SuggestFieldEvaluation:
    """
    对外接口：如果字段内容为空，则直接返回 feedback，否则进行评估。
    """
    if not field_content:
        return SuggestFieldEvaluation(
            field=field,
            suggested="",
            reason="",
            feedback="字段不存在或为空"
        )
    return await evaluate_suggest_priority_field(field, field_content, workspace_id)
