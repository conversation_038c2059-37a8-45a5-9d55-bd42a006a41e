# file: backend/app/utils/field_evaluation_models.py

from pydantic import BaseModel
from typing import Optional

class SuggestFieldEvaluation(BaseModel):
    """
    通用的“LLM 字段评估”返回模型：
    - field:  要评估的字段名
    - suggested:  LLM 推荐的值，比如 “high”/“low” 或 “severity_high” 之类
    - reason:     LLM 给出的理由或背景说明
    - feedback:   如果过程中有报错或反馈信息，则该字段非空
    """
    field: str
    suggested: Optional[str]
    actual: Optional[str] = ""
    reason: Optional[str]
    feedback: Optional[str]
