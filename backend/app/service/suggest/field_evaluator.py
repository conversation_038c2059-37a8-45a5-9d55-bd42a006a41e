# file: backend/app/utils/field_evaluator.py

from typing import Any
import re
import json
from backend.app.service.hunyuan import evaluate_field_with_llm
from backend.app.utils.logger_util import logger
from backend.app.service.suggest.field_evaluation_models import SuggestFieldEvaluation
async def _evaluate_field_with_llm(
    field_name: str,
    field_content: dict,
    json_key: str,
    workspace_id: str = ""
) -> SuggestFieldEvaluation:
    """
    通用函数：调用混元模型 evaluate_field_with_llm，
    并尝试从返回内容里提取 json_key 对应的值，以及 reason。
    最终返回一个 SuggestFieldEvaluation。
    """
    # 1. 调用混元模型接口
    content, error = await evaluate_field_with_llm(field_name, field_content, workspace_id)
    if error:
        # 如果模型本身返回了 error，则直接把 feedback 填上
        return SuggestFieldEvaluation(
            field=field_name,
            suggested="",
            reason="",
            feedback=error
        )

    # 2. 尝试把 content 当成完整 JSON 解析
    try:
        data = json.loads(content)
        suggested = data.get(json_key, "")
        reason = data.get("reason", "无详细原因")
        return SuggestFieldEvaluation(
            field=field_name,
            suggested=suggested or "",
            reason=reason or "",
            feedback=""
        )
    except json.JSONDecodeError as e:
        # 解析失败时，先记录详细日志
        logger.error(f"[JSONDecodeError] 解析失败：{e.msg}")
        logger.error(f"出错位置：pos={e.pos}, line={e.lineno}, column={e.colno}")
        logger.error(f"原始文本片段：{e.doc[e.pos:e.pos+40]!r}")

        # 3. 尝试用正则提取最外层的 JSON 片段
        json_match = re.search(r'(\{.*\})', content, re.DOTALL)
        if json_match:
            try:
                inner = json.loads(json_match.group(1))
                suggested = inner.get(json_key, "")
                reason = inner.get("reason", "无详细原因")
                return SuggestFieldEvaluation(
                    field=field_name,
                    suggested=suggested or "",
                    reason=reason or "",
                    feedback=""
                )
            except (json.JSONDecodeError, ValueError) as inner_e:
                logger.error(f"[Inner JSONDecodeError] 内部 JSON 解析失败：{str(inner_e)}")

        # 4. 如果上述都失败，则使用正则单独抽取字段
        suggested = ""
        reason = "无详细原因"
        # 抽取 suggest 字段
        pattern_suggest = rf'"{json_key}"\s*:\s*"([^"]+)"'
        m_suggest = re.search(pattern_suggest, content)
        if m_suggest:
            suggested = m_suggest.group(1)
            logger.info(f"{json_key} = {suggested}")
        # 抽取 reason 字段
        m_reason = re.search(r'"reason"\s*:\s*"([^"]+)"', content)
        if m_reason:
            reason = m_reason.group(1)
            logger.info(f"reason = {reason}")

        return SuggestFieldEvaluation(
            field=field_name,
            suggested=suggested,
            reason=reason,
            feedback=""
        )
    except Exception as e:
        # 捕获其它意外异常
        logger.error(f"[Exception] {_evaluate_field_with_llm.__name__} 出错: {str(e)}")
        return SuggestFieldEvaluation(
            field=field_name,
            suggested="",
            reason="",
            feedback=f"评估过程出错: {str(e)}"
        )
