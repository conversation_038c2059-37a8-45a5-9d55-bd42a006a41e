from openai import OpenAI
import re
from backend.app.utils.logger_util import logger
from backend.app.config.config import (
    HUNYUAN_API_URL, 
    HUNYUAN_API_KEY, 
    HUNYUAN_MODEL,
)
import asyncio
from backend.app.prompts.description_prompt import (
    get_field_prompt_stage1,
    get_field_prompt_stage2,
)
from backend.app.prompts.title_prompt import (  
    get_bug_title_three_element_need_prompt,
    get_bug_title_quality_prompt_stage1,     
    get_bug_title_quality_prompt_stage2,
)   
from backend.app.prompts.priority_prompt import get_priority_suggestion_system_prompt
from backend.app.prompts.severity_prompt import get_severity_suggestion_system_prompt       

client = OpenAI(
    base_url=HUNYUAN_API_URL,
    api_key=HUNYUAN_API_KEY,
)

MAX_RETRIES = 3
RETRY_INTERVAL_SECONDS = 60

async def evaluate_field_with_llm(field_name: str, field_content: dict, workspace_id, **kwargs) -> tuple[str, str]:
    if not HUNYUAN_API_KEY:
        logger.error(f"[hunyuan.evaluate_field_with_llm] 未配置腾讯混元API密钥，无法进行评估")
        return "", "未配置腾讯混元API密钥，无法进行评估"

    # 构造 prompt 文本
    stage = kwargs.get("stage", 1)
    image_content = kwargs.get("image_content", "")  # 仅用于拼 prompt

    try:
        if field_name == "详细描述":
            if stage == 1:
                prompt = get_field_prompt_stage1(field_name, field_content, image_content, workspace_id)
            else:
                stage1 = kwargs.get("stage1", {})
                prompt = get_field_prompt_stage2(field_name, field_content, stage1, image_content, workspace_id)
        elif field_name == "标题":
            if stage == "three_elements":
                prompt = get_bug_title_three_element_need_prompt(field_content, workspace_id)
            elif stage == 1:
                three_elements_result = kwargs.get("three_elements_result", {})
                prompt = get_bug_title_quality_prompt_stage1(field_content, three_elements_result, workspace_id)
            elif stage == 2:
                stage1 = kwargs.get("stage1", {})
                prompt = get_bug_title_quality_prompt_stage2(field_content, stage1, workspace_id)
        elif field_name == "优先级":
            prompt = get_priority_suggestion_system_prompt(field_content, workspace_id)
        elif field_name == "严重程度":
            prompt = get_severity_suggestion_system_prompt(field_content, workspace_id)
        else:
            raise NotImplementedError(f"字段 {field_name} 的评估未实现")

        logger.info(field_name + ": " + prompt)

        # 重试机制
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                response = client.chat.completions.create(
                    model=HUNYUAN_MODEL,
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": prompt
                                }
                            ]
                        }
                    ],
                    temperature=0.0,
                    stream=False,
                    extra_body={
                        "enable_deep_search": True,
                        "enable_enhancement": True,
                    }
                )
                content = response.choices[0].message.content.strip()
                content = await clean_output(content)
                return content, ""

            except Exception as inner_e:
                logger.error(f"[尝试 {attempt}/{MAX_RETRIES}] 字段 {field_name} 调用模型失败: {str(inner_e)}")
                if attempt < MAX_RETRIES:
                    logger.info(f"将在 {RETRY_INTERVAL_SECONDS} 秒后重试...")
                    await asyncio.sleep(RETRY_INTERVAL_SECONDS)
                else:
                    raise inner_e

    except Exception as e:
        logger.error(f"[hunyuan.evaluate_field_with_llm] 评估字段 {field_name} 时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return "", f"评估字段 {field_name} 时发生错误: {str(e)}"

async def clean_output(content: str) -> str:
    # 尝试清理和修复JSON
    # 1. 去除可能的代码块标记
    content = re.sub(r'^```json\s*|\s*```$', '', content)
    # 2. 确保内容以 { 开始，} 结束
    content = content.strip()
    if not content.startswith('{'):
        json_start = content.find('{')
        if json_start != -1:
            content = content[json_start:]
    if not content.endswith('}'):
        json_end = content.rfind('}')
        if json_end != -1:
            content = content[:json_end+1]

    # 打印清理后的内容，便于调试
    logger.info(f"[LLM Response] 清理后的内容: {content}")
    return content