from typing import List
import re
import json
from backend.app.utils.logger_util import logger
from backend.app.config.config import (
    DESCRIPTION_DIMENSIONS,
    HUNYUAN_API_KEY,
    HUNYUAN_API_URL,
    PASSING_THRESHOLD,
    EXTRACT_IMAGE_MAXNUM
)   
from backend.app.service.hunyuan import evaluate_field_with_llm
from pydantic import BaseModel
from typing import Dict
import requests
from backend.app.utils.tapd import tap_client
from openai import OpenAI
import base64
from backend.app.prompts.picture_prompt import get_hunyuan_version_prompt
import ast
class DimensionFieldEvaluation(BaseModel):
    field: str
    dimension_scores: Dict[str, float]  # 添加各维度得分
    score: float = 0.0  # 总分，默认为0
    passed: bool
    feedback: str
    suggest: str = ""  # 添加suggest字段，默认为空字符串

def construct_text_message(
    creator: str,
    bug_title: str,
    bug_link: str,
    common_missing: List[str],
    failed_evaluations: List[DimensionFieldEvaluation]
) -> str:
    """构建文本消息"""
    message = f"<@{creator}> 你有一个缺陷提交不规范～，请检查并修改。\n"
    message += f"缺陷标题：{bug_title}\n"
    message += f"缺陷链接：{bug_link}\n"
    
    # 添加缺失的普通字段
    if common_missing:
        message += f"\n缺失字段：{', '.join(common_missing)}\n"
    
    # 添加特殊字段评估结果
    if failed_evaluations:
        message += "\n特殊字段评估结果：\n"
        for eval in failed_evaluations:
            # 添加字段名称
            message += f"━━━━━━ {eval.field} ━━━━━━\n"
            
            # 添加各维度评级
            message += "维度评级：\n"
            for dim, score in eval.dimension_scores.items():
                # 根据分数范围显示不同的评级
                if score >= 0.9:
                    rating = "优秀"
                    emoji = "🟢"
                elif score >= 0.8:
                    rating = "良好"
                    emoji = "🟢"
                elif score >= 0.6:
                    rating = "一般"
                    emoji = "🟡"
                else:
                    rating = "较差"
                    emoji = "🔴"
                message += f"{emoji} {dim}: {rating}\n"
            
            # 添加反馈信息，去除原来的维度得分部分
            feedback = eval.feedback
            if "各维度得分：" in feedback:
                parts = feedback.split("\n\n", 1)
                if len(parts) > 1:
                    feedback = parts[1]
            
            message += f"\n改进建议：\n{feedback}\n\n"
    
    return message

async def evaluate_dimension_field_stage1(field_name, field_content, image_content, workspace_id) -> Dict:
    """评估维度字段第一阶段"""
    content, error = await evaluate_field_with_llm(field_name, field_content, workspace_id, stage=1, image_content=image_content)
    if error != "":
        return {}
    try:
        try:
            # 首先尝试标准 JSON 解析
            result = json.loads(content)
        except json.JSONDecodeError:
            # 如果失败，尝试使用 ast.literal_eval 处理类 JSON 格式
            result = ast.literal_eval(content)

        dimension_scores = result.get("dimension_scores", {})
        feedback = result.get("feedback", "无详细反馈")

        return {
            "dimension_scores": dimension_scores,
            "feedback": feedback,
        }
    except Exception as e:
        logger.error(f"[解析失败] {e}")
        logger.error(f"原始文本：{content}")

        # 如果JSON/AST提取失败，尝试正则提取
        dimension_scores = {}

        for dimension in DESCRIPTION_DIMENSIONS:
            pattern = re.compile(f"['\"]{dimension}['\"]\\s*:\\s*([0-9.]+)", re.IGNORECASE)
            match = pattern.search(content)
            if match:
                try:
                    dimension_scores[dimension] = float(match.group(1))
                except ValueError:
                    dimension_scores[dimension] = 0
            else:
                dimension_scores[dimension] = 0
        logger.info("尝试2: 正则提取各维度得分：" + str(dimension_scores))

        feedback_match = re.search(r"['\"]feedback['\"]\s*:\s*['\"]((?:\\.|[^\"\\])*)['\"]", content)
        if feedback_match:
            feedback = feedback_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            logger.info("尝试2.1: 提取JSON格式的反馈信息：" + feedback)
        else:
            feedback = "无法提取反馈信息"
            feedback_patterns = [r"['\"]feedback['\"]\s*:\s*['\"]([\s\S]+?)['\"]"]
            for pattern in feedback_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    feedback = match.group(1).strip()
                    logger.info("尝试2.2: 使用正则提取到反馈信息：" + feedback)
                    break

            if feedback == "无法提取反馈信息":
                logger.info("-------------尝试提取数字列表形式的反馈-------------")
                numbered_feedback = re.findall(r'\d+\.\s*(.*?)(?=\n\d+\.|\n\n|\Z)', content)
                if numbered_feedback:
                    feedback = "\n".join([f"{i+1}. {item.strip()}" for i, item in enumerate(numbered_feedback)])
                    logger.info("尝试2.3: 提取到数字列表形式的反馈：" + feedback)

        return {
            "dimension_scores": dimension_scores,
            "feedback": feedback,
        }

async def evaluate_dimension_field_stage2(field_name, field_content, stage1, image_content, workspace_id) -> str:
    content, error = await evaluate_field_with_llm(
        field_name, field_content, workspace_id, stage=2, stage1=stage1, image_content=image_content
    )
    if error != "":
        return ""

    try:
        result = json.loads(content)
        suggest_dict = result.get("suggest", {})
        if isinstance(suggest_dict, dict):
            # 拼接每个子字段，格式为：字段名 + 内容
            suggest_parts = [f"{key}\n{value}" for key, value in suggest_dict.items()]
            return "\n\n".join(suggest_parts)
        elif isinstance(suggest_dict, str):
            return suggest_dict
        else:
            return ""
    except Exception as e:
        logger.error(f"[JSONDecodeError] 解析失败：{e.msg}")
        logger.error(f"出错位置：pos={e.pos}, line={e.lineno}, column={e.colno}")
        logger.error(f"原始文本片段：{e.doc[e.pos:]!r}")

        # 尝试 fallback：正则提取 suggest 字典结构
        suggest_dict_match = re.search(r'"suggest"\s*:\s*{([^{}]+)}', content)
        if suggest_dict_match:
            suggest_content = suggest_dict_match.group(1)

            # 匹配形如："【步骤】": "xxx"
            field_matches = re.findall(r'"(【[^】]+】)"\s*:\s*"((?:\\.|[^"\\])*)"', suggest_content)

            if field_matches:
                suggest_parts = []
                for field, value in field_matches:
                    # 处理转义符
                    cleaned_value = value.replace('\\"', '"').replace('\\n', '\n')
                    suggest_parts.append(f"{field}\n{cleaned_value}")
                return "\n\n".join(suggest_parts)

        # fallback to old flat string match
        suggest_match = re.search(r'"suggest"\s*:\s*"((?:\\.|[^"\\])*)"', content)
        if suggest_match:
            suggest = suggest_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            logger.info("尝试3: 提取旧格式的 suggest 信息：" + suggest)
            return suggest

        return ""

async def get_image_url_from_bug(field_content):
    pattern = r'!\[\]\(([^)]+\.png)\)'
    matches = re.findall(pattern, field_content)
    return matches


async def url_to_base64(image_url):
    """
    将图片URL转换为base64编码字符串
    参数:
        image_url: 图片的URL地址
    返回:
        base64编码的图片字符串
    """
    try:
        # 下载图片
        response = requests.get(image_url)
        response.raise_for_status()
        
        # 获取图片内容类型
        content_type = response.headers.get('content-type', 'image/jpeg')
        if "html" in content_type:
            return ""
        # 转换为base64
        image_data = response.content
        base64_str = base64.b64encode(image_data).decode('utf-8')
        
        # 添加数据URI前缀
        if 'png' in content_type:
            prefix = 'data:image/png;base64,'
        elif 'jpeg' in content_type or 'jpg' in content_type:
            prefix = 'data:image/jpeg;base64,'
        else:
            prefix = 'data:image/png;base64,'  # 默认当作png处理
            
        return prefix + base64_str

    except requests.RequestException as e:
        logger.error(f"图片下载失败: {str(e)}")
        raise ValueError(f"无法下载或转换图片: {str(e)}")


async def call_hunyuan_vision(image_url: str, bug_data, origin_url: str) -> str:
    # 构造 client（API Key 和 endpoint）
    client = OpenAI(
        api_key=HUNYUAN_API_KEY,
        base_url=HUNYUAN_API_URL,
    )
    prompt = get_hunyuan_version_prompt(bug_data, origin_url)
    logger.info("prompt: " + prompt)
    # 发起请求
    completion = client.chat.completions.create(
        model="hunyuan-vision",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            },
        ],
    )
    return completion.choices[0].message.content

async def extract_image_urls(image_urls, bug_data, new_url_old_url_dict) -> Dict:
    image_content = {}
    for image_url in image_urls:
        base64url = await url_to_base64(image_url)
        if base64url == "":
            continue
        origin_url = new_url_old_url_dict[image_url]
        result = await call_hunyuan_vision(image_url=base64url, bug_data=bug_data, origin_url=origin_url)
        image_content[image_url] = result
    return image_content

async def evaluate_dimension_field(field_name: str, field_content: str, workspace_id: str) -> DimensionFieldEvaluation:
    raw_image_urls = await get_image_url_from_bug(field_content[field_name])
    raw_image_urls = list(set(raw_image_urls))
    new_url_old_url_dict = {}
    real_image_urls = []
    raw_image_url_content = {}
    if raw_image_urls:
        for url in raw_image_urls:
            image_url = ""
            if url.startswith("http://") or url.startswith("https://"):
                continue
            else:
                image_url = tap_client.get_image(workspace_id, url)
            logger.info(f"获取图片内容: {image_url}")
            new_url_old_url_dict[image_url] = url
            real_image_urls.append(image_url)
    if real_image_urls and len(real_image_urls) <= EXTRACT_IMAGE_MAXNUM:
        image_content = await extract_image_urls(real_image_urls, field_content, new_url_old_url_dict)
        for image_url, content in image_content.items():
            raw_image_url_content[new_url_old_url_dict[image_url]] = content
        logger.info(f"{field_name}的图片描述：" + json.dumps(raw_image_url_content, indent=4, ensure_ascii=False))
    stage1 = await evaluate_dimension_field_stage1(field_name, field_content, raw_image_url_content, workspace_id)
    dimension_scores = stage1.get("dimension_scores", {})
    # 判断是否有任意维度小于PASSING_THRESHOLD
    passed = all(dim_score >= PASSING_THRESHOLD for dim_score in dimension_scores.values())
    if not passed:
        suggest = await evaluate_dimension_field_stage2(field_name, field_content, stage1, raw_image_url_content, workspace_id)
        return DimensionFieldEvaluation(
            field=field_name,
            dimension_scores=stage1["dimension_scores"],
            passed=False,
            feedback=stage1["feedback"],
            suggest=suggest
        )
    else:
        return DimensionFieldEvaluation(
            field=field_name,
            dimension_scores=stage1["dimension_scores"],
            passed=True,
            feedback=stage1["feedback"],
            suggest=""
        )
    
async def get_description_score(field, field_content, workspace_id) -> DimensionFieldEvaluation:
    if not field_content:
        # 如果字段不存在或为空，直接标记为不通过
        default_scores = {dim: 0.0 for dim in DESCRIPTION_DIMENSIONS.keys()}
        return DimensionFieldEvaluation(
                field=field,
                score=0.0,
                dimension_scores=default_scores,
                passed=False,
                feedback="字段不存在或为空",
                suggest=""  # 添加空的suggest字段
            )
    else:
        # 使用大模型评估字段内容质量
        evaluation = await evaluate_dimension_field(field, field_content, workspace_id)
        return evaluation