from typing import Dict
from pydantic import BaseModel
import re
from backend.app.service.hunyuan import evaluate_field_with_llm
import json
from backend.app.config.config import PASSING_THRESHOLD, TITLE_DIMENSIONS
from backend.app.utils.logger_util import logger
class TitleFieldEvaluation(BaseModel):
    field: str
    dimension_scores: dict
    passed: bool
    feedback: str
    suggest: str
    thinking_stage1: str = ""  # 第一阶段的思维过程
    thinking_stage2: str = ""  # 第二阶段的思维过程
    needed_elements: list = []  # 三要素必要性判断结果
    element_reason: str = ""  # 三要素判断原因


async def evaluate_title_three_elements_need(field_name, field_content, workspace_id) -> Dict:
    """三要素必要性判断"""
    content, error = await evaluate_field_with_llm(field_name, field_content, workspace_id, stage="three_elements")
    if error != "":
        return {"needed_elements": [], "reason": ""}
    try:
        result = json.loads(content)
        needed_elements = result.get("needed_elements", [])
        reason = result.get("reason", "")
        return {
            "needed_elements": needed_elements,
            "reason": reason
        }
    except Exception as e:
        logger.error(f"[JSONDecodeError] 三要素判断解析失败：{str(e)}")
        return {"needed_elements": [], "reason": ""}

async def evaluate_title_field_stage1(field_name, field_content, three_elements_result, workspace_id) -> Dict:
    """标题评估第一阶段"""
    content, error = await evaluate_field_with_llm(field_name, field_content, workspace_id, stage=1, three_elements_result=three_elements_result)
    if error != "":
        return {}
    try:
        result = json.loads(content)
        dimension_scores = result.get("dimension_scores", {})
        feedback = result.get("feedback", "无详细反馈")
        thinking_process = result.get("thinking_process", "")
        return {
            "dimension_scores": dimension_scores,
            "feedback": feedback,
            "thinking_process": thinking_process
        }
    except Exception as e:
        logger.error(f"[JSONDecodeError] 解析失败：{str(e)}")
        # 尝试为每个配置的维度提取得分
        dimension_scores = {}
        for dimension in TITLE_DIMENSIONS:
            pattern = re.compile(f'"{dimension}"\\s*:\\s*([0-9.]+)', re.IGNORECASE)
            match = pattern.search(content)
            if match:
                try:
                    dimension_scores[dimension] = float(match.group(1))
                except ValueError:
                    dimension_scores[dimension] = 0
            else:
                dimension_scores[dimension] = 0
        logger.info("尝试2: 正则提取各维度得分：" + str(dimension_scores))
        feedback_match = re.search(r'"feedback"\\s*:\\s*"((?:\\.|[^"\\])*)"', content)
        if feedback_match:
            # 处理转义字符
            feedback = feedback_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            logger.info("尝试2.1: 提取JSON格式的反馈信息：" + feedback)
        else:
            # 如果没有找到JSON格式的feedback，尝试提取文本形式的反馈
            feedback_patterns = [
                r'"feedback"\s*:\s*"([\s\S]+?)"'
            ]
            feedback = "无法提取反馈信息"
            for pattern in feedback_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    feedback = match.group(1).strip()
                    logger.info("尝试2.2: 使用正则提取到反馈信息：" + feedback)
                    break

            # 如果上述模式都没匹配到，尝试提取数字列表形式的反馈
            if feedback == "无法提取反馈信息":
                logger.info("-------------尝试提取数字列表形式的反馈-------------")
                numbered_feedback = re.findall(r'\d+\.\s*(.*?)(?=\n\d+\.|\n\n|\Z)', content)
                if numbered_feedback:
                    feedback = "\n".join([f"{i+1}. {item.strip()}" for i, item in enumerate(numbered_feedback)])
                    logger.info("尝试2.3: 提取到数字列表形式的反馈：" + feedback)

        # 尝试提取thinking_process字段
        thinking_process = ""
        thinking_process_match = re.search(r'"thinking_process"\s*:\s*"((?:\\.|[^"\\])*)"', content)
        if thinking_process_match:
            # 处理转义字符
            thinking_process = thinking_process_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            logger.info("尝试2.4: 提取JSON格式的思维过程：" + thinking_process)
        else:
            # 如果没有找到JSON格式的thinking_process，尝试提取文本形式的思维过程
            thinking_process_patterns = [
                r'"thinking_process"\s*:\s*"([\s\S]+?)"',
                r'"thinking_process"\s*:\s*"([^"]*)"'
            ]
            for pattern in thinking_process_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    thinking_process = match.group(1).strip()
                    logger.info("尝试2.5: 使用正则提取到思维过程：" + thinking_process)
                    break

        return {
            "feedback": feedback,
            "dimension_scores": dimension_scores,
            "thinking_process": thinking_process  # 返回提取到的思维过程
        }

async def evaluate_title_field_stage2(field_name, field_content, stage1, workspace_id) -> Dict:
    content, error = await evaluate_field_with_llm(field_name, field_content, workspace_id, stage=2, stage1=stage1)
    if error != "":
        return {"suggest": "", "thinking_process": ""}
    try:
        result = json.loads(content)
        suggest = result.get("suggest", "")
        thinking_process = result.get("thinking_process", "")
        return {"suggest": suggest, "thinking_process": thinking_process}
    except Exception as e:
        logger.error(f"[JSONDecodeError] 解析失败：{str(e)}")
        # 尝试提取suggest字段
        suggest = ""
        suggest_match = re.search(r'"suggest"\s*:\s*"((?:\\.|[^"\\])*)"', content)
        if suggest_match:
            # 处理转义字符
            suggest = suggest_match.group(1).replace('\\"', '"').replace('\\n', '\n')
        logger.info("正则提取标题suggest: " + suggest)

        # 尝试提取thinking_process字段
        thinking_process = ""
        thinking_process_match = re.search(r'"thinking_process"\s*:\s*"((?:\\.|[^"\\])*)"', content)
        if thinking_process_match:
            # 处理转义字符
            thinking_process = thinking_process_match.group(1).replace('\\"', '"').replace('\\n', '\n')
            logger.info("正则提取标题阶段2思维过程: " + thinking_process)
        else:
            # 如果没有找到JSON格式的thinking_process，尝试提取文本形式的思维过程
            thinking_process_patterns = [
                r'"thinking_process"\s*:\s*"([\s\S]+?)"',
                r'"thinking_process"\s*:\s*"([^"]*)"'
            ]
            for pattern in thinking_process_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    thinking_process = match.group(1).strip()
                    logger.info("使用正则提取到阶段2思维过程：" + thinking_process)
                    break

        return {"suggest": suggest, "thinking_process": thinking_process}  # 返回提取到的思维过程

async def evaluate_title_field(field_name: str, field_content: str, workspace_id: str) -> TitleFieldEvaluation:
    """使用三要素判断+两阶段CoT思维链评估标题字段质量"""
    try:
        # 三要素必要性判断
        three_elements_result = await evaluate_title_three_elements_need(field_name, field_content, workspace_id)
        needed_elements = three_elements_result.get("needed_elements", [])
        element_reason = three_elements_result.get("reason", "")

        # 第一阶段：评估和反馈
        stage1 = await evaluate_title_field_stage1(field_name, field_content, needed_elements, workspace_id)
        dimension_scores = stage1.get("dimension_scores", {})
        # 判断是否有任意维度小于PASSING_THRESHOLD
        passed = all(dim_score >= PASSING_THRESHOLD for dim_score in dimension_scores.values())

        if not passed:
            # 第二阶段：生成建议
            stage2_result = await evaluate_title_field_stage2(field_name, field_content, stage1, workspace_id)
            return TitleFieldEvaluation(
                field=field_name,
                dimension_scores=stage1["dimension_scores"],
                passed=False,
                feedback=stage1["feedback"],
                suggest=stage2_result["suggest"],
                thinking_stage1=stage1.get("thinking_process", ""),
                thinking_stage2=stage2_result["thinking_process"],
                needed_elements=needed_elements,
                element_reason=element_reason
            )
        else:
            return TitleFieldEvaluation(
                field=field_name,
                dimension_scores=stage1["dimension_scores"],
                passed=True,
                feedback=stage1["feedback"],
                suggest="",
                thinking_stage1=stage1.get("thinking_process", ""),
                thinking_stage2="",  # 通过时不需要第二阶段
                needed_elements=needed_elements,
                element_reason=element_reason
            )

    except Exception as e:
        # 异常情况下返回错误评估结果
        logger.error(f"[Exception] 评估过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return TitleFieldEvaluation(
            field=field_name,
            dimension_scores={},
            passed=False,
            feedback=f"评估过程出错: {str(e)}",
            suggest="无建议标题",
            thinking_stage1="",
            thinking_stage2="",
            needed_elements=[],
            element_reason=""
        )
    
async def get_title_score(field, field_content, workspace_id) -> TitleFieldEvaluation:
    if not field_content:
        return TitleFieldEvaluation(
                field=field,
                dimension_scores={},
                passed=False,
                feedback="字段不存在或为空",
                suggest="无建议标题",
                thinking_stage1="",
                thinking_stage2="",
                needed_elements=[],
                element_reason=""
            )
    else:
        # 使用大模型评估字段内容质量
        evaluation = await evaluate_title_field(field, field_content, workspace_id)
        return evaluation