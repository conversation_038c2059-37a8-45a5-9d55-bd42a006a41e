import time
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

class LoggerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 开始时间
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算执行时间
        process_time = time.time() - start_time
        
        # 打印日志
        print(f"[FASTAPI] {time.strftime('%Y/%m/%d - %H:%M:%S')} | "
              f"{response.status_code} | {process_time:.3f}s | "
              f"{request.method} | {request.url.path}")
        
        return response 