{"健康": {"普通字段": {"标题": true, "严重程度": true, "模块": true, "处理人": true, "重现规律": true, "优先级": true, "需求链接": true, "发现阶段": true, "是否线上问题": true, "迭代": true}, "特殊字段": {"详细描述": true, "标题": true, "优先级": true, "严重程度": true}}, "默认规则": {"标题": false, "详细描述": false, "严重程度": false, "模块": false, "状态": false, "创建人": false, "创建时间": false, "最后修改时间": false, "处理人": false, "重现规律": false, "优先级": false, "需求链接": false, "需求名称": false, "解决时间": false, "关闭时间": false, "开发人员": false, "验证版本": false, "发现版本": false, "解决方法": false, "缺陷根源": false, "发现阶段": false, "验证人": false, "软件平台": false, "操作系统": false, "测试类型": false, "测试阶段": false, "测试人员": false, "是否线上问题": false, "预计解决时间": false, "发布计划": false, "验证时间": false, "拒绝时间": false, "重新打开时间": false, "预计结束": false, "预计开始": false, "解决期限": false, "接受处理时间": false, "分配时间": false, "完成工时": false, "预估工时": false, "剩余工时": false, "超出工时": false, "缺陷引入来源": false, "缺陷逃逸原因": false, "是否漏测": false, "漏测原因说明": false, "用户反馈数量": false, "反馈来源": false, "是否自动化有效问题": false, "审核人": false, "修复人": false, "关闭人": false, "参与人": false, "引入阶段": false, "迭代": false, "缺陷类型": false, "ID": false, "label": false, "规模": false, "延期解决原因": false, "拒绝原因": false}}