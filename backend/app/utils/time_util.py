"""
时间工具模块
提供统一的时间处理函数
"""
from datetime import datetime
import pytz

# 设置中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def get_china_now():
    """获取中国时区的当前时间"""
    return datetime.now(CHINA_TZ)

def utc_to_china(utc_time):
    """将UTC时间转换为中国时间"""
    if utc_time is None:
        return None
    
    # 如果是naive datetime，假设它是UTC时间
    if utc_time.tzinfo is None:
        utc_time = pytz.utc.localize(utc_time)
    
    # 转换为中国时间
    return utc_time.astimezone(CHINA_TZ)

def china_to_utc(china_time):
    """将中国时间转换为UTC时间"""
    if china_time is None:
        return None
    
    # 如果是naive datetime，假设它是中国时间
    if china_time.tzinfo is None:
        china_time = CHINA_TZ.localize(china_time)
    
    # 转换为UTC时间
    return china_time.astimezone(pytz.utc)
