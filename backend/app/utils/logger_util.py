import logging
import os
from logging.handlers import TimedRotatingFileHandler

# 日志目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
print(f"当前脚本所在目录: {BASE_DIR}")
LOG_DIR = os.path.join(BASE_DIR, "../logs")
os.makedirs(LOG_DIR, exist_ok=True)  # 如果没有 log 目录就自动创建

# 创建 logger
logger = logging.getLogger("bug_conform")
logger.setLevel(logging.DEBUG)

# 日志格式：包含时间、级别、文件名、行号、函数名、消息
formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
)

# 控制台输出 handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(formatter)

# ✅ 每天生成一个新文件，保留最近7天日志
file_handler = TimedRotatingFileHandler(
    filename=os.path.join(LOG_DIR, "bug_conform.log"),
    when='midnight',       # 每天午夜创建新日志
    interval=1,            # 间隔 1 天
    backupCount=7,         # 最多保留 7 个旧日志文件
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)

# 避免重复添加 handler
if not logger.handlers:
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
logger.info("✅ 日志系统初始化成功，写入第一条日志")
file_handler.flush()
