# 依赖管理说明

## 依赖文件说明

### requirements.txt
生产环境依赖，包含运行应用所需的所有包：

- **Web框架**: FastAPI, Uvicorn, Pydantic
- **认证安全**: python-jose, passlib
- **数据库**: SQLAlchemy, PyMySQL
- **HTTP客户端**: httpx, requests
- **网页解析**: BeautifulSoup4, html2text, markdownify
- **AI模型**: langchain, openai
- **数据处理**: pandas, openpyxl
- **配置管理**: python-dotenv
- **其他工具**: pytz, pycryptodome

### requirements-dev.txt
开发环境依赖，包含测试和开发工具：

- **测试框架**: pytest, pytest-asyncio, pytest-cov
- **代码质量**: black, flake8, isort, mypy
- **开发工具**: ipython, jupyter, pdb++

## 安装依赖

### 生产环境
```bash
cd backend
pip install -r requirements.txt
```

### 开发环境
```bash
cd backend
pip install -r requirements-dev.txt
```

### 使用依赖管理脚本
```bash
# 安装生产环境依赖
python scripts/manage_dependencies.py install

# 安装开发环境依赖
python scripts/manage_dependencies.py install-dev

# 升级依赖
python scripts/manage_dependencies.py upgrade

# 检查过时依赖
python scripts/manage_dependencies.py check

# 生成依赖快照
python scripts/manage_dependencies.py freeze

# 安全检查
python scripts/manage_dependencies.py security
```

## 依赖更新策略

1. **定期检查**: 每月检查一次过时的依赖
2. **安全更新**: 立即应用安全补丁
3. **主版本升级**: 谨慎处理，需要充分测试
4. **测试覆盖**: 更新依赖后运行完整测试套件

## 添加新依赖

1. 确定依赖的用途（生产 vs 开发）
2. 添加到相应的 requirements 文件
3. 指定版本号（推荐固定版本）
4. 更新此文档说明
5. 运行测试确保兼容性

## 依赖分类说明

### 核心依赖
- FastAPI: Web框架
- SQLAlchemy: ORM
- Pydantic: 数据验证

### 功能依赖
- openai: AI模型接口
- pandas: 数据处理
- httpx: HTTP客户端

### 工具依赖
- python-dotenv: 环境变量管理
- pytz: 时区处理
- pycryptodome: 加密功能

## 故障排除

### 常见问题

1. **依赖冲突**: 使用虚拟环境隔离依赖
2. **版本不兼容**: 检查依赖的版本要求
3. **安装失败**: 确保系统依赖已安装

### 解决方案

```bash
# 清理pip缓存
pip cache purge

# 重新安装所有依赖
pip uninstall -r requirements.txt -y
pip install -r requirements.txt

# 使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```
