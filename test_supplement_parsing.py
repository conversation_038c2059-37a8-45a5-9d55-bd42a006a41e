#!/usr/bin/env python3
import re
import sys
import json

# 添加项目路径
sys.path.append('.')

supplement_content = '''    入口or菜单：     
    日志信息：
    抓包信息：
trace_id: "9124f163-115b-3465-e1da-198740238c29"
点击看原图'''

def parse_yiyao_supplement_test(content: str) -> dict:
    """
    解析医药SaaS其他补充的细粒度字段
    """
    result = {}
    
    print(f"输入内容: {repr(content)}")
    print("=" * 50)

    # 子字段模式（包括截图/录屏）- 修改为匹配到下一个字段标题之前的所有内容
    sub_patterns = {
        "入口or菜单": r"入口or菜单\s*[：:]\s*(.*?)(?=日志信息|抓包信息|截图[/／]录屏|$)",
        "日志信息": r"日志信息\s*[：:]\s*(.*?)(?=入口or菜单|抓包信息|截图[/／]录屏|$)",
        "抓包信息": r"抓包信息\s*[：:]\s*(.*?)(?=入口or菜单|日志信息|截图[/／]录屏|$)",
        "截图/录屏": r"截图[/／]录屏\s*[：:]\s*(.*?)(?=入口or菜单|日志信息|抓包信息|$)"
    }

    for field_name, pattern in sub_patterns.items():
        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
        if match:
            field_content = match.group(1).strip()
            # 保持原有的换行格式，只清理多余的空行
            field_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', field_content)
            # 只清理每行开头和结尾的空格，但保留换行符
            lines = field_content.split('\n')
            lines = [line.strip() for line in lines]
            field_content = '\n'.join(lines)
            print(f"匹配到 {field_name}: {repr(field_content)}")
            if field_content:
                result[field_name] = field_content
        else:
            print(f"未匹配到 {field_name}")

    # 如果没有匹配到任何子字段，将整个内容作为补充信息返回
    if not result and content.strip():
        result["补充信息"] = content.strip()
        print(f"作为补充信息: {repr(content.strip())}")

    return result

print("测试其他补充字段解析:")
result = parse_yiyao_supplement_test(supplement_content)
print("\n解析结果:")
print(json.dumps(result, ensure_ascii=False, indent=2))

# 测试完整的医药SaaS解析
print("\n" + "="*50)
print("测试完整的医药SaaS解析:")

full_content = '''【前提条件】

设备机型：macos
登录账号/角色信息：CMS管理员
问题发生时间：2025年7月28号
【问题现象】

1、公众号后台新增或者删除模板，NCEP管理端不能实时同步（目前缓存时间为12小时）

【重现步骤】
1、公众号后台新增或者删除模板后，查看NCEP管理端-模板消息推送-新建模板消息的模板列表

【预期结果】
1、公众号后台新增或者删除模板后，查看NCEP管理端-模板消息推送-新建模板消息的模板列表，能够实时或者较快同步最新模板列表

【其他补充】（便于开发定位问题的一些其他信息，补充在这里）
    入口or菜单：
    日志信息：
    抓包信息：
trace_id: "9124f163-115b-3465-e1da-198740238c29"
点击看原图'''

try:
    from backend.app.utils.tapd import parse_yiyao_saas_description
    full_result = parse_yiyao_saas_description(full_content)
    print(json.dumps(full_result, ensure_ascii=False, indent=2))
except Exception as e:
    print(f'导入失败: {e}')
