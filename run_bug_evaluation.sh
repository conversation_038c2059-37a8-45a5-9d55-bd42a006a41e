#!/bin/bash

# BUG规范性评估脚本运行器
# 该脚本从环境变量读取配置参数，并运行pull_bugs_evaluation.py
# 环境变量应该在流水线中提前加载好

set -e  # 遇到错误立即退出

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$SCRIPT_DIR/backend"
PYTHON_SCRIPT="$BACKEND_DIR/app/scripts/pull_bugs_evaluation.py"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1" >&2
}

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    log_error "Python脚本不存在: $PYTHON_SCRIPT"
    exit 1
fi

# 切换到backend目录
cd "$BACKEND_DIR"
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
log_info "使用流水线预加载的环境变量"

# 构建Python命令参数
PYTHON_ARGS=()

# 处理时间参数（互斥选项，按优先级处理）
if [ -n "$BUG_EVALUATION_PAST_DAYS" ]; then
    log_info "使用过去天数模式: $BUG_EVALUATION_PAST_DAYS 天"
    PYTHON_ARGS+=("--past-days" "$BUG_EVALUATION_PAST_DAYS")
elif [ "$BUG_EVALUATION_YESTERDAY" = "true" ]; then
    log_info "使用昨天模式"
    PYTHON_ARGS+=("--yesterday")
elif [ "$BUG_EVALUATION_TODAY" = "true" ]; then
    log_info "使用今天模式"
    PYTHON_ARGS+=("--today")
elif [ -n "$BUG_EVALUATION_START_TIME" ]; then
    log_info "使用自定义时间范围: $BUG_EVALUATION_START_TIME ~ ${BUG_EVALUATION_END_TIME:-现在}"
    PYTHON_ARGS+=("--start-time" "$BUG_EVALUATION_START_TIME")
    if [ -n "$BUG_EVALUATION_END_TIME" ]; then
        PYTHON_ARGS+=("--end-time" "$BUG_EVALUATION_END_TIME")
    fi
else
    log_error "必须指定时间参数之一："
    log_error "  - BUG_EVALUATION_PAST_DAYS: 过去多少天"
    log_error "  - BUG_EVALUATION_YESTERDAY=true: 昨天"
    log_error "  - BUG_EVALUATION_TODAY=true: 今天"
    log_error "  - BUG_EVALUATION_START_TIME: 自定义开始时间"
    exit 1
fi

# 添加其他可选参数
if [ -n "$BUG_EVALUATION_WORKSPACE_ID" ]; then
    log_info "使用工作区ID: $BUG_EVALUATION_WORKSPACE_ID"
    PYTHON_ARGS+=("--workspace-id" "$BUG_EVALUATION_WORKSPACE_ID")
fi

if [ -n "$BUG_EVALUATION_MAX_BUGS" ]; then
    log_info "最大处理数量: $BUG_EVALUATION_MAX_BUGS"
    PYTHON_ARGS+=("--max-bugs" "$BUG_EVALUATION_MAX_BUGS")
fi

if [ -n "$BUG_EVALUATION_RETRY_COUNT" ]; then
    log_info "重试次数: $BUG_EVALUATION_RETRY_COUNT"
    PYTHON_ARGS+=("--retry-count" "$BUG_EVALUATION_RETRY_COUNT")
fi

if [ -n "$BUG_EVALUATION_BATCH_SIZE" ]; then
    log_info "批处理大小: $BUG_EVALUATION_BATCH_SIZE"
    PYTHON_ARGS+=("--batch-size" "$BUG_EVALUATION_BATCH_SIZE")
fi

if [ -n "$BUG_EVALUATION_LOG_LEVEL" ]; then
    log_info "日志级别: $BUG_EVALUATION_LOG_LEVEL"
    PYTHON_ARGS+=("--log-level" "$BUG_EVALUATION_LOG_LEVEL")
fi

if [ -n "$BUG_EVALUATION_SKIP_MODE" ]; then
    log_info "跳过模式: $BUG_EVALUATION_SKIP_MODE"
    PYTHON_ARGS+=("--skip-mode" "$BUG_EVALUATION_SKIP_MODE")
fi

if [ "$BUG_EVALUATION_DRY_RUN" = "true" ]; then
    log_info "启用试运行模式"
    PYTHON_ARGS+=("--dry-run")
fi

if [ "$BUG_EVALUATION_NOTIFY_ONLY" = "true" ]; then
    log_info "启用仅群聊通知模式"
    PYTHON_ARGS+=("--notify-only")
fi

if [ "$BUG_EVALUATION_BUG" = "true" ]; then
    log_info "启用仅群聊通知模式"
    PYTHON_ARGS+=("--evaluate-bug")
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    log_error "Python3 未安装或不在PATH中"
    exit 1
fi

# 显示即将执行的命令
log_info "即将执行命令: python3 app/scripts/pull_bugs_evaluation.py ${PYTHON_ARGS[*]}"

# 执行Python脚本
log_info "开始执行BUG评估任务..."
python3 app/scripts/pull_bugs_evaluation.py "${PYTHON_ARGS[@]}"

# 检查执行结果
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    log_info "BUG评估任务执行成功"
else
    log_error "BUG评估任务执行失败，退出码: $EXIT_CODE"
    exit $EXIT_CODE
fi
