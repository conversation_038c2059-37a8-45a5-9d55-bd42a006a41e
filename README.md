# BugConform

BugConform是一个基于FastAPI的Web应用程序，用于管理和跟踪软件缺陷。

## 技术栈

### 后端
- FastAPI - 现代、快速的Web框架
- SQLAlchemy - SQL工具包和ORM
- Pydantic - 数据验证和设置管理
- Uvicorn - ASGI服务器
- Python-Jose - JWT令牌处理
- Passlib - 密码哈希

## 项目结构

```
.
├── backend/           # 后端代码
│   ├── app/          # 应用程序代码
│   ├── scripts/      # 脚本文件
│   ├── utils/        # 工具函数
│   ├── main.py       # 主入口文件
│   └── requirements.txt  # Python依赖
└── bugs/             # 缺陷相关文件
```

## 安装说明

1. 克隆仓库
```bash
git clone [repository-url]
cd BugConform
```

2. 设置后端环境
```bash
cd backend
python -m venv venv
source venv/bin/activate  # 在Windows上使用: venv\Scripts\activate

# 安装生产环境依赖
pip install -r requirements.txt

# 或者安装开发环境依赖（包含测试和开发工具）
pip install -r requirements-dev.txt

# 使用 Makefile 简化操作
make install      # 安装生产环境依赖
make install-dev  # 安装开发环境依赖
make check-deps   # 检查依赖是否正确安装
```

3. 配置环境变量
```bash
cp .env_template .env
# 编辑.env文件，填入必要的配置信息
```

4. 运行应用
```bash
python main.py
```

服务器将在 http://localhost:8080 启动

## 依赖管理

项目使用分层的依赖管理策略：

### 依赖文件
- `requirements.txt` - 生产环境依赖
- `requirements-dev.txt` - 开发环境依赖（包含测试和开发工具）
- `backend/DEPENDENCIES.md` - 详细的依赖管理说明

### 常用命令
```bash
cd backend

# 使用 Makefile（推荐）
make help         # 查看所有可用命令
make install      # 安装生产环境依赖
make install-dev  # 安装开发环境依赖
make check-deps   # 检查依赖状态
make upgrade      # 升级依赖
make security     # 安全检查
make clean        # 清理缓存

# 或使用依赖管理脚本
python3 scripts/manage_dependencies.py install
python3 scripts/check_dependencies.py
```

## API文档

启动服务器后，可以访问以下地址查看API文档：
- Swagger UI: http://localhost:8080/docs
- ReDoc: http://localhost:8080/redoc

## 许可证

[待定]